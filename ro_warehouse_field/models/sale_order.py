# -*- coding: utf-8 -*-

from odoo import models, fields, api,_
from odoo.exceptions import UserError



class SaleOrder(models.Model):
    _inherit = 'sale.order'


    warehouse_id = fields.Many2one(
        'stock.warehouse', string='Warehouse',
        store=True, readonly=False, precompute=True,required=False,
        states={'sale': [('readonly', True)], 'done': [('readonly', True)], 'cancel': [('readonly', False)]},
        check_company=True)
    

    @api.depends('user_id', 'company_id')
    def _compute_warehouse_id(self):
        pass
    
    
    def action_confirm(self):
        for record in self:
            if not record.warehouse_id:
                raise UserError(
                    _('Back House is Required'))
            else:
                return super(SaleOrder,self).action_confirm()
    
 