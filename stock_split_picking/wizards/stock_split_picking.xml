<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_stock_split_picking" model="ir.ui.view">
        <field name="name">stock.split.picking.form</field>
        <field name="model">stock.split.picking</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="mode" invisible="True"/>
                    <field name="picking_ids" invisible="True"/>
                    <field name="parent_code" invisible="True"/>
                    <field name="picking_type_id"/>
                    <field name="location_id"/>
                    <field name="location_dest_id"/>
                </group>

                <group>
                    <field
                    name="move_ids"
                    invisible="mode != 'selection'"
                    required="mode != 'selection'"
                    domain="[('picking_id', 'in', picking_ids)]"
                    >
                    <tree string="Moves" create="true" edit="true" delete="true" editable="bottom">
                        <field name="product_id" readonly="1"/>
                        <field name="product_uom_qty" readonly="1"/>
                        <field name="ro_next_split" readonly="0"/>
                        <field readonly="1"
                            name="product_uom"
                            options="{'no_open': True, 'no_create': True}"
                            string="Unit of Measure"
                            groups="uom.group_uom"
                        />
                        <field name="picking_id" readonly="1"/>
                        <field
                            name="location_id" readonly="1"
                            options="{'no_create': True}"
                            groups="stock.group_stock_multi_locations"
                        />
                        <field
                            name="location_dest_id" readonly="1"
                            options="{'no_create': True}"
                            groups="stock.group_stock_multi_locations"
                        />
                        <field name="create_date" groups="base.group_no_one" readonly="1"/>
<!--                            <field-->
<!--                                name="date"-->
<!--                                string="Date"-->
<!--                                groups="base.group_no_one"-->
<!--                            />-->
<!--                            <field name="date_deadline" />-->
                        <field name="state" readonly="1"/>
                    </tree>
                </field>
                </group>
                <footer>
                <button
                        name="action_apply"
                        class="btn btn-primary"
                        string="Split"
                        type="object"
                    />
                or
                <button special="cancel" class="btn btn-secondary" string="Cancel" />
            </footer>
            </form>
        </field>
    </record>

    <record id="action_stock_split_picking" model="ir.actions.act_window">
        <field name="name">Split pickings</field>
        <field name="res_model">stock.split.picking</field>
        <field name="view_id" ref="view_stock_split_picking" />
        <field name="target">new</field>
    </record>
</odoo>
