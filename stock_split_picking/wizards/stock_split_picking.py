
from odoo import fields, models, api, _


class StockSplitPicking(models.TransientModel):
    _name = "stock.split.picking"
    _description = "Split a picking"

    mode = fields.Selection(
        [
            #("done", "Done quantities"),
            #("move", "One picking per move"),
            ("selection", "Select move lines to split off"),
        ],
        required=True,
        default="selection",
    )
    picking_type_id = fields.Many2one('stock.picking.type', string="Operation Type", readonly=False,
                                      domain="[('code', '=', parent_code)]", default=False)
    location_id = fields.Many2one('stock.location', "Source Location", compute="_compute_location_id",
                                  store=True, readonly=False)
    location_dest_id = fields.Many2one('stock.location', "Destination Location",
                                       compute="_compute_location_id", readonly=False, store=True)
    picking_ids = fields.Many2many(
        "stock.picking",
        default=lambda self: self._default_picking_ids(),
    )
    parent_type_id = fields.Many2one('stock.picking.type', related='picking_ids.picking_type_id')
    parent_code = fields.Selection(related='parent_type_id.code')
    move_ids = fields.Many2many("stock.move")

    @api.onchange('picking_type_id')
    def _compute_location_id(self):
        for picking in self:
            if picking.picking_type_id:
                if picking.picking_type_id.default_location_src_id:
                    location_id = picking.picking_type_id.default_location_src_id.id
                else:
                    _customerloc, location_id = self.env['stock.warehouse']._get_partner_locations()

                if picking.picking_type_id.default_location_dest_id:
                    location_dest_id = picking.picking_type_id.default_location_dest_id.id
                else:
                    location_dest_id, _supplierloc = self.env['stock.warehouse']._get_partner_locations()

                picking.location_id = location_id
                picking.location_dest_id = location_dest_id

    def _default_picking_ids(self):
        return self.env["stock.picking"].browse(self.env.context.get("active_ids", []))

    def action_apply(self):
        return getattr(self, "_apply_%s" % self[:1].mode)()

    def _apply_done(self):
        return self.mapped("picking_ids").split_process()

    def _apply_move(self):
        """Create new pickings for every move line, keep first
        move line in original picking
        """
        new_pickings = self.env["stock.picking"]
        for picking in self.mapped("picking_ids"):
            for move in picking.move_lines[1:]:
                new_pickings += picking._split_off_moves(move, self)
        return self._picking_action(new_pickings)

    def _apply_selection(self):
        """Create one picking for all selected moves"""
        moves = self.mapped("move_ids")
        new_picking = moves.mapped("picking_id")._split_off_moves(moves, self)
        return self._picking_action(new_picking)

    def _picking_action(self, pickings):
        action = self.env["ir.actions.act_window"]._for_xml_id(
            "stock.action_picking_tree_all",
        )
        action["domain"] = [("id", "in", pickings.ids)]
        return action
