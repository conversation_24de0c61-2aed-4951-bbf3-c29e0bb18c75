<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_picking_form" model="ir.ui.view">
        <field name="name">Stock picking out Split button</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <field name="state" position="before">
                <button
                    name="%(stock_split_picking.action_stock_split_picking)s"
                    statusbar_visible="draft,confirmed,assigned"
                    string="Split"
                    groups="stock_split_picking.stock_split_picking_groups"
                    type="action"
                />
            </field>
        </field>
    </record>
</odoo>
