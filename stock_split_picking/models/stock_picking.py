
from odoo import _, models, fields
from odoo.exceptions import UserError
from odoo.tools.float_utils import float_compare


class StockMove(models.Model):
    _inherit = "stock.move"

    ro_next_split = fields.Float("Split")


class StockPicking(models.Model):
    """Adds picking split without done state."""

    _inherit = "stock.picking"

    def split_process(self):
        print("split_process 13")
        """Use to trigger the wizard from button with correct context"""
        #mrmzz
        # Split moves considering the qty_done on moves
        new_moves = self.env["stock.move"]
        move_create = []

        for picking in self:

            # Check the picking state and condition before split
            if picking.state == "draft":
                raise UserError(_("Mark as todo this picking please."))
            if all([x.qty_done == 0.0 for x in picking.move_line_ids]):
                raise UserError(
                    _(
                        "You must enter done quantity in order to split your "
                        "picking in several ones."
                    )
                )

            # mrmzz
            # Split moves considering the qty_done on moves
            # new_moves = self.env["stock.move"]

            for move in picking.move_ids:
                rounding = move.product_uom.rounding
                qty_done = move.quantity_done
                qty_initial = move.product_uom_qty
                qty_diff_compare = float_compare(
                    qty_done, qty_initial, precision_rounding=rounding
                )
                if qty_diff_compare < 0:
                    qty_split = qty_initial - qty_done
                    qty_uom_split = move.product_uom._compute_quantity(
                        qty_split, move.product_id.uom_id, rounding_method="HALF-UP"
                    )
                    new_move_vals = move._split(qty_uom_split)
                    for move_line in move.move_line_ids:
                        if move_line.product_qty and move_line.qty_done:
                            # To avoid an error
                            # when picking is partially available
                            try:
                                move_line.write({"product_uom_qty": move_line.qty_done})
                            except UserError:
                                pass
                    # mrmzz
                    # new_move = self.env["stock.move"].create(new_move_vals)
                    # new_move._action_confirm(merge=False)
                    # new_moves |= new_move
                    move_create.append(new_move_vals)
            # mrmzz
            new_move = self.env["stock.move"].create(move_create)
            new_move._action_confirm(merge=False)
            new_moves |= new_move

            # If we have new moves to move, create the backorder picking
            if new_moves:
                backorder_picking = picking._create_split_backorder()
                new_moves.write({"picking_id": backorder_picking.id})
                new_moves.mapped("move_line_ids").write(
                    {"picking_id": backorder_picking.id}
                )
                new_moves._action_assign()

    def _create_split_backorder(self, default=None, split=False):
        """Copy current picking with defaults passed, post message about
        backorder"""
        self.ensure_one()
        if split.picking_type_id:
            ptype = split.picking_type_id
        else:
            ptype = self.picking_type_id

        backorder_picking = self.copy(
            dict({"name": ptype.sequence_id.next_by_id() if ptype.sequence_id else "/",
                  "move_ids": [],
                  "picking_type_id": ptype.id,
                  "location_id": split.location_id.id if split.location_id else self.location_id.id,
                  "location_dest_id": split.location_dest_id.id if split.location_dest_id else self.location_dest_id.id,
                  "move_line_ids": [],
                  "backorder_id": self.id,
                  }, **(default or {}))
        )
        self.message_post(
            body=_(
                'The backorder <a href="#" '
                'data-oe-model="stock.picking" '
                'data-oe-id="%d">%s</a> has been created.'
            )
            % (backorder_picking.id, backorder_picking.name)
        )
        return backorder_picking

    def _split_off_moves(self, moves, split=False):
        """Remove moves from pickings in self and put them into a new one"""
        new_picking = self.env["stock.picking"]
        for this in self:
            if this.state in ("done", "cancel"):
                raise UserError(_("Cannot split picking %s in state %s") % (this.name, this.state))
            new_picking = new_picking or this._create_split_backorder(split=split)
            # print("moves1111")
            # print(sum(moves.mapped('product_uom_qty')))
            # print(moves.mapped('ro_next_split'))
            #
            if not sum(moves.mapped('product_uom_qty')) - sum(moves.mapped('ro_next_split')) and not this.move_ids - moves:
                raise UserError(
                    _("Cannot split off all moves from picking %s") % this.name
                )
            if sum(moves.mapped('ro_next_split')) == 0:
                raise UserError(
                    _("Cannot split off Zero Qty from picking %s") % this.name
                )
            for m in moves:
                if m.product_uom_qty == m.ro_next_split:
                    m.write({"picking_id": new_picking.id})
                    m.mapped("move_line_ids").write({"picking_id": new_picking.id})
                elif m.product_uom_qty != m.ro_next_split:
                    new_move = m.copy()
                    m.write({"product_uom_qty": m.product_uom_qty - m.ro_next_split})
                    new_move.write({"picking_id": new_picking.id, "product_uom_qty": m.ro_next_split})
                else:
                    return False
                # print(m)
                # print(new_move)
                # print(rrrrrrrrrrrrrrrrrrrr)

        # print(wwwwwwwwwww)
        # moves.write({"picking_id": new_picking.id})
        # moves.mapped("move_line_ids").write({"picking_id": new_picking.id})
        new_picking.action_confirm()
        return new_picking
