from odoo import api, fields, models, _

class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    farm_name = fields.Many2one('purchase.receipt.farm', string="اسم المزرعة")
    housha_number = fields.Many2one('purchase.receipt.housha', string="رقم الحوشة")
    collection_date = fields.Date(string="تاريخ التحصيل")
    
    def write(self, vals):
        res = super(StockPicking, self).write(vals)
        if 'farm_name' in vals or 'housha_number' in vals or 'collection_date' in vals:
            for picking in self.filtered(lambda x:x.picking_type_code=='incoming' and x.purchase_id):
                picking.ensure_one()
                farm_name = picking.farm_name.name
                housha_name = picking.housha_number.name
                collection_date = picking.collection_date
                new_lot_value=""
                if farm_name:
                    new_lot_value += f"{farm_name}"
                if housha_name:
                    new_lot_value += f" حوشة رقم {housha_name}"
                if collection_date:
                    new_lot_value += f" تاريخ جمع {collection_date}"
                for move in picking.move_ids_without_package:
                    product = move.product_id
                    if product.tracking == 'lot':
                        lot_obj = self.env['stock.lot']
                        lot = lot_obj.search([
                            ('name', '=', new_lot_value),
                            ('product_id', '=', product.id)
                        ], limit=1)
                        if not lot:
                            lot = lot_obj.create({
                                'name': new_lot_value,
                                'product_id': product.id,
                                'company_id': picking.company_id.id,  # optional but good practice
                            })

                        for move_line in move.move_line_ids:
                            move_line.lot_name = new_lot_value 
                            move_line.lot_id = lot.id
        return res