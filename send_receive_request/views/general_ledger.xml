<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- <record id="general_ledger_cash_in_out_action" model="ir.actions.act_window">
            <field name="name">General Ledger</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.move.line</field>
            <field name="view_mode">tree,pivot,graph</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note')), '|', '|', ('account_id.user_type_id.name','=','Bank and Cash'), ('account_id.user_type_id.name','=','بنك ونقدية'), ('account_id.user_type_id.name','=','البنك والنقد ')]</field> 
            <field name="search_view_id" ref="account.view_account_move_line_filter_with_root_selection"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('account.view_move_line_tree_grouped_general')})]"/>
            <field name="context">{'journal_type':'general', 'search_default_group_by_account': 1, 'search_default_posted':1}</field>
        </record>
        <menuitem id="menu_action_general_ledger_cash_in_out_action" name="General Ledger" action="general_ledger_cash_in_out_action" parent="send_receive_request.menu_in_out_payment" groups="account.group_account_user"/>
        <menuitem id="menu_action_general_ledger_cash_in_out_action" name="General Ledger" action="general_ledger_cash_in_out_action" parent="send_receive_request.menu_in_out_payment" groups="account.group_account_user,sales_team.group_sale_salesman"/> -->
<!--***********************************************************************************************************-->
        <record id="general_ledger_report_inherit" model="account.report">
            <field name="name">General Ledger</field>
            <field name="filter_journals" eval="True"/>
            <field name="filter_analytic" eval="True"/>
            <field name="filter_period_comparison" eval="False"/>
            <field name="filter_multi_company">selector</field>
            <field name="filter_unfold_all" eval="True"/>
            <field name="default_opening_date_filter">this_month</field>
            <field name="search_bar" eval="True"/>
            <field name="load_more_limit" eval="80"/>
            <field name="main_template">account_reports.template_general_ledger_report</field>
            <field name="line_template">account_reports.line_template_general_ledger_report</field>
            <field name="custom_handler_model_id" ref="model_account_general_ledger_report_handler_inherit"/>
            <field name="column_ids">
                <record id="general_ledger_report_date" model="account.report.column">
                    <field name="name">Date</field>
                    <field name="expression_label">date</field>
                    <field name="figure_type">none</field>
                </record>
                <record id="general_ledger_report_communication" model="account.report.column">
                    <field name="name">Communication</field>
                    <field name="expression_label">communication</field>
                    <field name="figure_type">none</field>
                </record>
                <record id="general_ledger_report_partner_name" model="account.report.column">
                    <field name="name">Partner</field>
                    <field name="expression_label">partner_name</field>
                    <field name="figure_type">none</field>
                </record>
                <record id="general_ledger_report_amount_currency" model="account.report.column">
                    <field name="name">Currency</field>
                    <field name="expression_label">amount_currency</field>
                    <field name="figure_type">monetary</field>
                </record>
                <record id="general_ledger_report_debit" model="account.report.column">
                    <field name="name">Debit</field>
                    <field name="expression_label">debit</field>
                    <field name="figure_type">monetary</field>
                </record>
                <record id="general_ledger_report_credit" model="account.report.column">
                    <field name="name">Credit</field>
                    <field name="expression_label">credit</field>
                    <field name="figure_type">monetary</field>
                </record>
                <record id="general_ledger_report_balance" model="account.report.column">
                    <field name="name">Balance</field>
                    <field name="expression_label">balance</field>
                    <field name="figure_type">monetary</field>
                </record>
            </field>
        </record>
        
        <record id="action_account_report_general_ledger" model="ir.actions.client">
            <field name="name">General Ledger Report</field>
            <field name="tag">account_report</field>
            <!-- <field name="context" eval="{'model': 'account.general.ledger.report.handler.inherit'}" /> -->
            <field name="context" eval="{'report_id': ref('send_receive_request.general_ledger_report_inherit')}"/>

        </record>
        <!-- <record id="action_account_report_general_ledger" model="ir.actions.client">
            <field name="name">General Ledger</field>
            <field name="tag">account_report</field>
            <field name="context" eval="{'report_id': ref('account_reports.general_ledger_report')}"/>
        </record> -->
        <menuitem id="menu_action_account_report_general_ledger" name="General Ledger Report" action="action_account_report_general_ledger" parent="send_receive_request.menu_in_out_payment"/>

        <menuitem id="menu_action_account_report_general_ledger_petty_cash_action" name="General Ledger Report" action="action_account_report_general_ledger" parent="send_receive_request.menu_petty_cash"/>
        
    </data>
</odoo>