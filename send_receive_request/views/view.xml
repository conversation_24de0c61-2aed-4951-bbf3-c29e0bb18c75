<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="view_acount_journal_form_inherit" model="ir.ui.view">
        <field name="name">Journal Form Inheri</field>
        <field name="model">account.journal</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="account.view_account_journal_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='country_code']" position="after">
                <field name="pay_type" invisible="type not in ('cash','bank')"/>
            </xpath>
        </field>
    </record>

    <!-- <record id="view_account_payment_plan_form_inherit" model="ir.ui.view">
        <field name="name">account_payment_plan Inheri</field>
        <field name="model">account.payment</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='journal_id']" position="after">
                <field name="plan_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
            </xpath>
        </field>
    </record> -->

    <record id="view_acount_payment_match_form_inherit" model="ir.ui.view">
        <field name="name">Payment match Form Inheri</field>
        <field name="model">account.payment</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="account_accountant.view_account_payment_form_inherit_account_accountant"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_open_manual_reconciliation_widget']" position="attributes">
                <attribute name="groups">account.group_account_invoice</attribute>
            </xpath>
        </field>
    </record>

    <record id="action_account_document_out_payment" model="ir.actions.act_window">
        <!-- <field name="type">ir.actions.act_window</field> -->
        <field name="name">Documents Out</field>
        <field name="res_model">account.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{ 'default_is_document': True,
                                'default_payment_type': 'outbound',
                                'default_post_type': 'multi',
                                'default_partner_type': 'supplier','journal':'out'}
        </field>
        <field name="domain">[('is_document','=',True), ('is_internal_transfer','=',False), ('payment_type','=', 'outbound')]</field>
    </record>

    <record id="action_account_document_in_payment" model="ir.actions.act_window">
        <!-- <field name="type">ir.actions.act_window</field> -->
        <field name="name">Documents In</field>
        <field name="res_model">account.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{ 'default_is_document': True,
                                'default_payment_type': 'inbound',
                                'default_partner_type': 'customer','journal':'in'}
        </field>
        
        <field name="domain">[('is_document','=',True), ('is_internal_transfer','=',False),('payment_type','=', 'inbound')]</field>
    </record>

    <record id="action_account_document_internal_payment" model="ir.actions.act_window">
        <!-- <field name="type">ir.actions.act_window</field> -->
        <field name="name">Internal Transfer</field>
        <field name="res_model">account.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{ 'default_is_document': True,
                                'default_payment_type': 'outbound',
                                'default_is_internal_transfer': True,}
        </field>
        <field name="domain">[('is_document','=',True),('is_internal_transfer','=', True)]</field>
    </record>

    <!-- out -->
    <record id="view_account_payment_document_out_tree" model="ir.ui.view">
        <field name="name">account.payment.document.out.tree</field>
        <field name="model">account.payment</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'" decoration-muted="state in ['reconciled', 'cancelled']"
                  edit="false">
                <field name="date"/>
                <field name="name"/>
                <field name="journal_id"/>
                <field name="payment_method_id"/>
                <field name="partner_id" string="Partner"/>
                <field name="amount" sum="Amount"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="currency_id" invisible="1"/>
                <field name="partner_type" invisible="1"/>
            </tree>
        </field>
    </record>
    <!-- in -->
    <record id="view_account_payment_document_in_tree" model="ir.ui.view">
        <field name="name">account.payment.document_in.tree</field>
        <field name="model">account.payment</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'" decoration-muted="state in ['reconciled', 'cancelled']"
                  edit="false">
                <field name="date"/>
                <field name="name"/>
                <field name="journal_id"/>
                <field name="payment_method_id"/>
                <field name="partner_id" string="Partner"/>
                <field name="amount" sum="Amount"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="currency_id" invisible="1"/>
                <field name="partner_type" invisible="1"/>
            </tree>
        </field>
    </record>

    <!-- internal -->
    <record id="view_account_payment_document_internal_tree" model="ir.ui.view">
        <field name="name">account.payment.document_internal.tree</field>
        <field name="model">account.payment</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'" decoration-muted="state in ['reconciled', 'cancelled']"
                  edit="false">
                <field name="date"/>
                <field name="name"/>
                <field name="journal_id"/>
                <field name="payment_method_id"/>
                <field name="partner_id" string="Partner"/>
                <field name="amount" sum="Amount"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="currency_id" invisible="1"/>
                <field name="partner_type" invisible="1"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="account_payment_document_out_inherited_view">
        <field name="name">account.payment.document.out.inherited.view</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="mode">primary</field>
        <field name="priority">30</field>
        <field name="arch" type="xml">
            
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="destination_account_id" options="{'no_create': True}" required="post_type == 'single'" invisible="post_type != 'single'" readonly="state != 'draft' or is_internal_transfer == True"/>

                <!-- <attribute name="attrs">{'required': [('post_type', '=', 'single')],'invisible': [('post_type','!=','single')]}</attribute> -->
            </xpath>
            <xpath expr="//field[@name='payment_type']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_type']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_bank_id']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='is_internal_transfer']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <!-- <xpath expr="//field[@name='move_id']" position="attributes">
                <attribute name="attrs">{'invisible': [('is_document', '=', True)]}</attribute>
            </xpath> -->

            <xpath expr="//field[@name='partner_id']" position="replace">
            </xpath>
            <xpath expr="//field[@name='partner_id']" position="replace">
            </xpath>
            <xpath expr="//field[@name='destination_account_id']" position="attributes">
                <attribute name="string">Account</attribute>
            </xpath>
            <xpath expr="//field[@name='destination_account_id']" position="after">
                <field name="partne_required" invisible="1"/>
                <field name="post_type"  invisible="payment_type == 'inbound'" readonly="state != 'draft'"/>
                <field name="partner_id" readonly="state != 'draft'" required="partne_required == True and post_type == 'single'" />
                <!-- 'invisible': [('post_type','!=','single')],  -->
                <field name="is_document" invisible="1"/>

            </xpath>

            <xpath expr="//group[@name='group2']" position="after">
                <group colspan='12'>
                    <field name="writeoff_multi_acc_ids" invisible="post_type == 'single'" readonly="state != 'draft'">
                        <tree editable="bottom">
                            <field name='writeoff_account_id'/>
                            <field name="partne_required" column_invisible="1"/>
                            <!-- <field name="required_analytical" invisible="1"/> -->
                            <field name='writeoff_partner_id' />
                            <field name='name'/>
                            <!-- attrs="{'required': [('required_analytical','=',True)]}" -->
                            <!-- <field name='analytic_account_id' />
                            <field name='analytic_tag_ids' widget='many2many_tags'/> -->
                            <!-- <field name="plan_id" invisible='1'/> -->
                            <field name="account_type" column_invisible='1'/>
                            
                            <field name="ro_product_id" />
                            <!-- <field name="analytic_account_id" attrs="{'required':[('account_type','in',['income','income_other','expense','expense_direct_cost'])]}" context="{'default_name':name}" />             -->
                            <field name="analytic_distribution" widget="analytic_distribution"
                                options="{'force_applicability': 'optional', 'disable_save': true}"/>
                            <field name='amount'/>
                            <field name='currency_id' column_invisible="1"/>
                        </tree>
                    </field>
                </group>

            </xpath>

            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="string">Partner</attribute>
            </xpath>


            <xpath expr="//field[@name='journal_id']" position="replace">
                
                <field name="journal" invisible='1'/>
                <!-- widget="selection" -->
                <field name="journal_id"
                    options='{"no_open": True, "no_create": True}'
                    domain="[('type','in',('cash','bank'))]"
                    readonly="state != 'draft'"/>
            </xpath>
            
        </field>
    </record>

    <record id="view_account_account_form_inherited" model="ir.ui.view">
        <field name="name">view.account.account.form.inherit</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='allowed_journal_ids']" position="after">
              <field name="is_customer"/>
              <field name="is_vendor"/>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="account_payment_document_in_inherited_view">
        <field name="name">account.payment.document.in.inherited.view</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="mode">primary</field>
        <field name="priority">30</field>
        <field name="arch" type="xml">
            

            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="destination_account_id" options="{'no_create': True}" required="post_type == 'single'" invisible="post_type != 'single'" readonly="state != 'draft' or is_internal_transfer == True"/>

                <!-- <attribute name="attrs">{'required': [('post_type', '=', 'single')],'invisible': [('post_type','!=','single')]}</attribute> -->
            </xpath>
            <xpath expr="//field[@name='payment_type']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_type']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_bank_id']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='is_internal_transfer']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <!-- <xpath expr="//field[@name='move_id']" position="attributes">
                <attribute name="attrs">{'invisible': [('is_document', '=', True)]}</attribute>
            </xpath> -->

            <xpath expr="//field[@name='partner_id']" position="replace">
            </xpath>
            <xpath expr="//field[@name='destination_account_id']" position="attributes">
                <attribute name="string">Account</attribute>
            </xpath>
            <xpath expr="//field[@name='destination_account_id']" position="after">
                <field name="partne_required" invisible="1"/>
                <field name="post_type" readonly="state != 'draft'" />
                <field name="partner_id" readonly="state != 'draft'" required="partne_required == True and post_type == 'single'"/>
                <!-- 'invisible': [('post_type','!=','single')],  -->
                <field name="is_document" invisible="1"/>
            </xpath>

            <xpath expr="//group[@name='group2']" position="after">
                <group colspan='12'>
                    <field name="writeoff_multi_acc_ids" invisible="post_type == 'single'" readonly="state != 'draft'">
                        <tree editable="bottom">
                            <field name='writeoff_account_id'/>
                            <field name="partne_required" column_invisible="1"/>
                            <!-- <field name="required_analytical" invisible="1"/> -->
                            <field name='writeoff_partner_id'/>
                             <!-- domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]" attrs="{'required': [('partne_required','=',True)]}" -->
                            <field name='name'/>
                            <!-- attrs="{'required': [('required_analytical','=',True)]}" -->
                            <!-- <field name='analytic_account_id' />
                            <field name='analytic_tag_ids' widget='many2many_tags'/> -->
                            <field name="analytic_distribution" widget="analytic_distribution"
                                options="{'force_applicability': 'optional', 'disable_save': true}"/>
                            <field name='amount'/>
                            <field name='currency_id' column_invisible="1"/>
                        </tree>
                    </field>
                </group>

            </xpath>

            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="string">Partner</attribute>
            </xpath>


            <xpath expr="//field[@name='journal_id']" position="replace">
                
                <!-- widget="selection" -->
                <field name="journal" invisible='1'/>
                <field name="journal_id"
                    options='{"no_open": True, "no_create": True}'
                    domain="[('type','in',('cash','bank'))]"
                    readonly="state != 'draft'"/>
            </xpath>
            
        </field>
    </record>

    <record model="ir.ui.view" id="account_payment_document_internal_inherited_view">
        <field name="name">account.payment.document.internal.inherited.view</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="mode">primary</field>
        <field name="priority">30</field>
        <field name="arch" type="xml">
            

            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="destination_account_id" options="{'no_create': True}" required='1' readonly="state != 'draft' or is_internal_transfer == True"/>
            </xpath>
             <xpath expr="//field[@name='payment_type']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_type']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>
            <xpath expr="//field[@name='partner_bank_id']" position="attributes">
                <attribute name="invisible">is_document == True</attribute>
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="replace">
            </xpath>
            <xpath expr="//field[@name='destination_account_id']" position="attributes">
                <attribute name="string">Account</attribute>
            </xpath>
            <xpath expr="//field[@name='destination_account_id']" position="after">
                <field name="partne_required" invisible="1"/>
                <field name="partner_id" readonly="state != 'draft'" required="partne_required == True"/>
                <field name="is_document" invisible="1"/>
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="string">Partner</attribute>
            </xpath>

            <xpath expr="//field[@name='journal_id']" position="replace">
                <!-- widget="selection" -->
                <field name="journal_id"
                    options='{"no_open": True, "no_create": True}'
                    domain="[('type','in',('cash','bank'))]"
                    readonly="state != 'draft'"/>
            </xpath>
            
        </field>
    </record>

    
    <record model="ir.actions.act_window.view" id="action_document_out_payment_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_account_payment_document_out_tree"/>
        <field name="act_window_id" ref="action_account_document_out_payment"/>
    </record> 
    <record model="ir.actions.act_window.view" id="action_document_in_payment_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_account_payment_document_in_tree"/>
        <field name="act_window_id" ref="action_account_document_in_payment"/>
    </record>
    <record model="ir.actions.act_window.view" id="action_document_internal_payment_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_account_payment_document_internal_tree"/>
        <field name="act_window_id" ref="action_account_document_internal_payment"/>
    </record>

    <record model="ir.actions.act_window.view" id="action_document_out_payment_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account_payment_document_out_inherited_view"/>
        <field name="act_window_id" ref="action_account_document_out_payment"/>
    </record>
    <record model="ir.actions.act_window.view" id="action_document_in_payment_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account_payment_document_in_inherited_view"/>
        <field name="act_window_id" ref="action_account_document_in_payment"/>
    </record>
    <record model="ir.actions.act_window.view" id="action_document_internal_payment_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="account_payment_document_internal_inherited_view"/>
        <field name="act_window_id" ref="action_account_document_internal_payment"/>
    </record>


    <menuitem
            id="menu_action_account_document_out_payment"
            action="action_account_document_out_payment"
            sequence="25"
            parent="account.menu_finance_payables"/>
    <menuitem
            id="menu_action_account_document_in_payment"
            action="action_account_document_in_payment"
            sequence="26"
            parent="account.menu_finance_receivables"/>
    <menuitem
            id="menu_action_account_document_internal_payment"
            action="action_account_document_internal_payment"
            sequence="26"
            parent="account.menu_finance_receivables"/>

    

    <menuitem
            id="menu_in_out_payment"
            name="Cash In/Out"
            sequence="25"
            groups="send_receive_request.group_cash_in_out"
            web_icon="fa fa-database,#FFFFFF,#34495e"/>
    <menuitem
            id="main_action_account_document_in_payment"
            name="Cash In"
            action="action_account_document_in_payment"
            sequence="1"
            parent="send_receive_request.menu_in_out_payment"/>    
    <menuitem
            id="main_action_account_document_out_payment"
            name="Cash Out"
            action="action_account_document_out_payment"
            sequence="2"
            parent="send_receive_request.menu_in_out_payment"/>
    <menuitem
            id="main_action_account_document_internal_payment"
            name="Internal Transfer"
            action="action_account_document_internal_payment"
            sequence="3"
            parent="send_receive_request.menu_in_out_payment"/>   


            

    <menuitem
        id="menu_petty_cash"
        name="Petty cash"
        sequence="26"
        groups="send_receive_request.group_petty_cash"
        web_icon="fa fa-book,#FFFFFF,#34495e"/>

    <menuitem
        id="main_action_account_petty_cash"
        name="Petty cash"
        action="action_account_document_out_payment"
        sequence="2"
        parent="send_receive_request.menu_petty_cash"/>
    
    <menuitem
        id="main_action_account_document_internal_petty_cash"
        name="Internal Transfer"
        action="action_account_document_internal_payment"
        sequence="3"
        parent="send_receive_request.menu_petty_cash"/>  


</odoo>
