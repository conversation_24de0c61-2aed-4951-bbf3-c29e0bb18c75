# -*- coding: utf-8 -*-

from odoo import fields, models, api, _, SUPERUSER_ID
from odoo.exceptions import UserError, ValidationError
from collections import defaultdict
from odoo.addons.sale.models.sale_order import SaleOrder


def _action_confirm(self):
    """ Implementation of additional mechanism of Sales Order confirmation.
        This method should be extended when the confirmation should generated
        other documents. In this method, the SO are in 'sale' state (not yet 'done').
    """
    # create an analytic account if at least an expense product
    for order in self:
        # if any(expense_policy not in [False, 'no'] for expense_policy in order.order_line.product_id.mapped('expense_policy')):
        if not order.analytic_account_id:
            # order._create_analytic_account()
            analytic = self.env['account.analytic.account'].create(order._prepare_analytic_account_so())
            order.analytic_account_id = analytic

            for line in order.order_line.filtered(lambda x:not x.display_type):
                
                to_creat = [line._prepare_analytic_account(order.analytic_account_id.name) for i in range(int(1))]
                # line.ro_no_container

                analytics = self.env['account.analytic.account'].create(to_creat)
                distributions = {}
                # percent = (100/line.ro_no_container) if line.ro_no_container else 100
                for analytic in analytics:
                    distributions[analytic.id] = 100
                    # percent
                line.analytic_distribution = distributions

SaleOrder._action_confirm = _action_confirm

class SaleOrderNew(models.Model):
    _inherit = 'sale.order'
    
    plan_id = fields.Many2one('account.analytic.plan',string='Project')


    
    def _prepare_analytic_account_so(self, prefix=None):
        """ Prepare SO analytic account creation values.

        :param str prefix: The prefix of the to-be-created analytic account name
        :return: `account.analytic.account` creation values
        :rtype: dict
        """
        self.ensure_one()
        name = self.name +'-'+ self.partner_id.name
        if prefix:
            name = prefix + ": " + self.name

        plan_name = self.name +'-'+ self.partner_id.name
        
        plan = self.env['account.analytic.plan'].search([('name','=',plan_name)], limit=1)
        if not plan:
            plan = self.env['account.analytic.plan'].sudo().create({
                'name': plan_name,
            })
            
        self.plan_id = plan
        
        return {
            'name': name,
            'code': self.client_order_ref,
            'company_id': self.company_id.id,
            'plan_id': plan.id,
            'partner_id': self.partner_id.id,
        }

   
class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    
    def _prepare_analytic_account(self, prefix=None):
        """ Prepare SO analytic account creation values.

        :param str prefix: The prefix of the to-be-created analytic account name
        :return: `account.analytic.account` creation values
        :rtype: dict
        """
        self.ensure_one()
        name = self.product_id.name
        if prefix:
            name = prefix +"-"+ self.product_id.name

        plan_name = self.order_id.name +'-'+ self.order_id.partner_id.name
        plan = self.env['account.analytic.plan'].search([('name','=',plan_name)], limit=1)
        if not plan:
            plan = self.env['account.analytic.plan'].create({
                'name': plan_name,
            })
        
        return {
            'name': name,
            'code': self.order_id.client_order_ref,
            'company_id': self.company_id.id,
            'plan_id': plan.id,
            'partner_id': self.order_id.partner_id.id,
        }
