<odoo>
    <data>
        <record id="view_crm_stage_sale_form" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header/field[@name='state']" position="before">
                    <field name="opportunity_id" invisible="1"/>
                    <field name="ro_crm_stage_id" widget="statusbar" options="{'clickable': '1', 'fold_field': 'fold'}" force_save="1" invisible="opportunity_id == False"/>
                </xpath>
            </field>
        </record>

        <record id="view_order_tree_inherit" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.sale_order_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="ro_crm_stage_id" optional="show"/>
                </xpath>
            </field>
        </record>

        <record id="view_sales_order_filter_inheret" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_sales_order_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='my_sale_orders_filter']" position="after">
                    <filter string="CRM Stage" domain="[('ro_crm_stage_id', '!=', False)]" name="my_sale_crm_stage_filter"/>
                </xpath>
                <xpath expr="//filter[@name='salesperson']" position="after">
                    <filter string="CRM Stage" name="group_by_ro_crm_stage_id" context="{'group_by': 'ro_crm_stage_id'}"/>
                </xpath>

            </field>
        </record>
    </data>
</odoo>