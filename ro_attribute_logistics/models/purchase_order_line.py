from odoo import models, fields, api

class SaleOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    ro_s_sline = fields.Char(string='Attr1', compute='_compute_attribute_values')
    ro_from = fields.Char(string='Attr2', compute='_compute_attribute_values')
    ro_to = fields.Char(string='Attr3', compute='_compute_attribute_values')

    @api.depends('product_id', 'product_id.product_template_attribute_value_ids')
    def _compute_attribute_values(self):
        for line in self:
            line.ro_s_sline = ''
            line.ro_from = ''
            line.ro_to = ''

            if line.product_id:
                attribute_values = line.product_id.product_template_attribute_value_ids
                attributes = [attr for attr in attribute_values if attr.attribute_id.ro_visible_logistics]

                if len(attributes) > 0:
                    line.ro_s_sline = attributes[0].name
                if len(attributes) > 1:
                    line.ro_from = attributes[1].name
                if len(attributes) > 2:
                    line.ro_to = attributes[2].name

    
