# -*- coding: utf-8 -*-
{
    'name': "Employee Predefine Fields",

    'summary': "Add predefine Fields in configration and  at employee tab",

    'description': """
        Add predefine Fields in configration and  at employee tab
    """,

    'author': "Roaya",
    'website': "https://www.roayadm.com",
    'category': 'hr',
    'version': '17.0',

   
    'depends': ['base','hr','ro_add_additional_info','ro_employee_available_info'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/class_view.xml',
        'views/job_title_view.xml',
        'views/point_view.xml',
        'views/degree_view.xml',
        'views/employee_view.xml',
        'views/grade_view.xml',
        'views/level_view.xml',

       
    ],
    'installable': True,
    'license': 'OPL-1', 
 
}

