# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, models


class Level(models.Model):
    _name = "hr.employee.level"
    _description = "Level"

    name = fields.Char(string="Level")
    # job_title_id = fields.Many2one('hr.employee.job.title',string="job title" ,required=True)
    # class_id = fields.Many2one('hr.employee.class',string="class" ,required=True)


    
    