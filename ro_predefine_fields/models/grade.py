# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, models


class Grade(models.Model):
    _name = "hr.employee.grade"
    _description = "Grade"


  
    name = fields.Char(string="Grade")
    # level_id = fields.Many2one('hr.employee.level',string="Level" ,required=True)
    # class_id = fields.Many2one('hr.employee.class',string="class" ,required=True)


    
    