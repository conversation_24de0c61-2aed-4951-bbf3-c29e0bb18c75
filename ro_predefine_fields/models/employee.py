from odoo import models, fields


class HrEmployeeBaseInherited(models.AbstractModel):
    _inherit = "hr.employee.base"
    ro_class = fields.Many2one('hr.employee.class',string='Class')
    ro_job_title = fields.Many2one('hr.employee.job.title', string='Job Title')
    ro_point = fields.Many2one('hr.employee.point', string='Point')
    ro_degree = fields.Many2one('hr.employee.degree', string='Degree')
    ro_grade = fields.Many2one('hr.employee.grade', string='Grade')
    ro_level = fields.Many2one('hr.employee.level', string='Level')








   

    







      

