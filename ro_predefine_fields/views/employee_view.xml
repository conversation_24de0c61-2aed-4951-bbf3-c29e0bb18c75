<odoo>
  <data>
           <record id="hr_employee_predefine_fields_form_inherited" model="ir.ui.view" >
            <field name="name">hr.employee.form.inherited</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <!-- <xpath expr="//field[@name='ro_email_private']" position="after"> -->

                                    <!-- <field name="ro_class" string="Class"/>
                                    <field name="ro_job_title" string="Job Title "  domain="[('class_id', '=', ro_class)]"/>   
                                    <field name="ro_point" string="Point"/>
                                    <field name="ro_degree" string="Degree"/> -->
                                    <!-- <field name="ro_level" string="Level"  domain="[('job_title_id', '=', ro_job_title)]"/> -->
                                    <!-- <field name="ro_level" string="Level"/> -->

                                    <!-- <field name="ro_grade" string="Grade" domain="[('level_id', '=', ro_level)]"/> -->
                                    <!-- <field name="ro_grade" string="Grade"/>

                                     -->
                                  
                              
                <!-- </xpath> -->

                <xpath expr="//page[@name='employee']" position="before">
                    <page name="class" string="Class">
                        <group>
                                <group name="clase_information">
                                  
                                    <field name="ro_class" string="Class"/>
                                    <field name="ro_job_title" string="Job Title "  domain="[('class_id', '=', ro_class)]"/>   
                                    <field name="ro_point" string="Point"/>

                                 
                                </group>
                                <group name="class_information">


                                    <field name="ro_degree" string="Degree"/>
                                    <field name="ro_level" string="Level"/>
                                    <field name="ro_grade" string="Grade"/>
                                   
                                 
                                </group>
                               
                   </group>
                </page>
                </xpath>
               
              
            </field>
        </record>
  </data>
</odoo>
