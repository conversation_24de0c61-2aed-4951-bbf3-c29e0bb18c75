<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_grade_tree_view" model="ir.ui.view">
            <field name="name">hr.grade.view.tree</field>
            <field name="model">hr.employee.grade</field>
            <field name="arch" type="xml">
                <tree string="grade">
                    
                    <field name="name" string="Grade" />
                    <!-- <field name="level_id" string="Level" /> -->
                    <!-- <field name="class_id" string="Class" /> -->

                    
                    
                </tree>
            </field>
        </record>
        <record id="hr_grade_form_view" model="ir.ui.view">
            <field name="name">hr.grade.view.form</field>
            <field name="model">hr.employee.grade</field>
            <field name="arch" type="xml">
                <form string="grade">
                    <sheet>
                        <group>
                            <field name="name" string="grade"></field>
                            <!-- <field name="level_id" string="Level" /> -->
                             <!-- <field name="class_id" string="Class" /> -->


                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="hr_grade_action" model="ir.actions.act_window">
            <field name="name">Grade</field>
            <field name="res_model">hr.employee.grade</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Grade
                </p>
            </field>
        </record>


         <menuitem
                id="menu_hr_grade"
                action="hr_grade_action"
                parent="hr.menu_config_employee"
                sequence="10"
              />
    </data>
</odoo>