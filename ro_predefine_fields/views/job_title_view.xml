<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_job_title_tree_view" model="ir.ui.view">
            <field name="name">hr.job.title.view.tree</field>
            <field name="model">hr.employee.job.title</field>
            <field name="arch" type="xml">
                <tree string="Job Title">
                    
                    <field name="name" string="Job Title" />
                    <field name="class_id" string="Class" />

                    
                </tree>
            </field>
        </record>
        <record id="hr_job_title_form_view" model="ir.ui.view">
            <field name="name">hr.job.title.form.view</field>
            <field name="model">hr.employee.job.title</field>
            <field name="arch" type="xml">
                <form string="Job Title">
                    <sheet>
                        <group>
                            <field name="name" string="Job Title"></field>
                            <field name="class_id" string="class"></field>

                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="hr_job_title_action" model="ir.actions.act_window">
            <field name="name">Job Title</field>
            <field name="res_model">hr.employee.job.title</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Job Title
                </p>
            </field>
        </record>


         <menuitem
                id="menu_hr_job_title"
                action="hr_job_title_action"
                parent="hr.menu_config_employee"
                sequence="10"
              />
    </data>
</odoo>