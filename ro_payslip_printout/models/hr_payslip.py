from odoo import models, fields, api, _
import base64

class HrPayslipInh(models.Model):
    _inherit = 'hr.payslip'
    
    def get_month_ar(self, month):
        months = {
            'January': 'يناير', 
            'February': 'فبراير',
            'March': 'مارس',
            'April': 'أبريل',
            'May': 'مايو',
            'June': 'يونيو',
            'July': 'يوليو',
            'August': 'أغسطس',
            'September': 'سبتمبر',
            'October': 'أكتوبر',
            'November': 'نوفمبر',
            'December': 'ديسمبر'
        }
        current_month = months.get(month)
        
        if current_month:
            return current_month
        else:
             pass
         
         
    def action_view_report(self):
        print(self)
        rows = []
        for line in self:

            employee = line.employee_id
            code =line.employee_id.ro_employee_id 
            employee = self.env['hr.employee'].search([('id', '=',employee.id)])
            employee_department_is_bloom=employee.department_id.ro_is_bloom
            incentive  =line.ro_incentive  
            medical_allowance  =line.ro_medical_allowance  
            ro_food_allowance  =line.ro_food_allowance  
            ro_transportation  =line.ro_transportation  
            ro_equalizations  =line.ro_equalizations  
            ro_penalties  =line.ro_penalties  
            ro_loans  =line.ro_loans  
            ro_splited_loans  =line.ro_splited_loans  
            # ro_discount_returns  =line.ro_discount_returns  
            ro_absence  =line.ro_absence 
            ro_discount_returns = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'family_med')
    
            # ro_basic  =line.line_ids.filtered(lambda x:x.salary_rule_id.rule_type == 'basic')
            basic_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'basic')
            bonus_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'bonus')
            social_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'social')
            net_salary_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'net_salary')
            incentive_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'incentive')
            med_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'med')
            food_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'food')
            trans_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'trans')
            equal_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'equal')
            penalties_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'penalties')
            loans_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'loans')
            long_loans_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'long_loans')
            family_med_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'family_med')
            absence_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'absence')
            late_total = sum(record.total for record in line.line_ids if record.salary_rule_id.rule_type == 'late')
            allocation_remaining =line.employee_id.allocation_remaining_display
            month_from = line.get_month_ar(line.date_from.strftime('%B'))
            month_to =line.get_month_ar(line.date_to.strftime('%B'))
            day_to =line.date_to.day
            year =line.date_from.year
            
                
            myrow = {
                "employee":employee.name,
                "code":code,
                "employee_department_is_bloom":employee_department_is_bloom,
                "incentive":incentive,
                "medical_allowance":medical_allowance,
                "ro_food_allowance":ro_food_allowance,
                "ro_transportation":ro_transportation,
                "ro_equalizations":ro_equalizations,
                "ro_penalties":ro_penalties,
                "ro_loans":ro_loans,
                "ro_splited_loans":ro_splited_loans,
                "ro_discount_returns":ro_discount_returns,
                "ro_absence":ro_absence,
                "basic_total":basic_total,
                "bonus_total":bonus_total,
                "social_total":social_total,
                "net_salary_total":net_salary_total,
                "incentive_total":incentive_total,
                "med_total":med_total,
                "food_total":food_total,
                "trans_total":trans_total,
                "equal_total":equal_total,
                "penalties_total":penalties_total,
                "loans_total":loans_total,
                "long_loans_total":long_loans_total,
                "family_med_total":family_med_total,
                "absence_total":absence_total,
                "late_total":late_total,
                "allocation_remaining":allocation_remaining,
                "month_from":month_from,
                "month_to":month_to,
                "year":year,
                "day_to":day_to,
            }
            rows.append(myrow)
        

        data = {
            'rows': rows,
        }
        return self.env.ref('ro_payslip_printout.action_print_payslip_report').report_action(self,data=data)
    
        
        
    