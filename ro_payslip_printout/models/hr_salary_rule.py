from odoo import models, fields,api 
from num2words import num2words

class HrSalaryRule(models.Model):
    _inherit = 'hr.salary.rule'

    rule_type = fields.Selection(
        string='Rule Type',
        selection=[('basic', 'Basic'),
                   ('bonus', 'Bonus'),
                   ('incentive','Incentive'),
                   ('med','Med'),
                   ('food','Food'),
                   ('trans','Trans'),
                   ('equal','Equal'),
                   ('reward','Reward'),
                   ('gross', 'Gross'),
                   ('absence','Absence'),
                   ('late','Late'),
                   ('penalties', 'Penalties'), 
                   ('long_loans', 'Long Loans'),
                   ('loans', 'Loans'), 
                   ('family_med','Family Med'),
                   ('social', 'Social'), 
                   ('attach_salary', 'Attach Salary'),
                   ('assign_salary', 'Assign Salary'), 
                   ('child_support', 'Child Support'), 
                   ('deduction', 'Deduction'),
                   ('reimbursement', 'Reimbursement'),
                   ('net_salary','Net Salary')]
    )
    