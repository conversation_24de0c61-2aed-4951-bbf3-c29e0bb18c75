from odoo import fields, models,api,_
from dateutil.relativedelta import relativedelta
from datetime import datetime, timedelta
from odoo.exceptions import UserError
import calendar

class Loan(models.Model):
    _name = "hr.employee.loan"
    _description = "Loan"


    name = fields.Char(string='loan')
    ro_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_department = fields.Many2one(related='ro_employee.department_id')
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency')
    ro_loan_amount = fields.Monetary(string='Loan Amount', currency_field='currency_id', required=True)
    ro_payment_start_date = fields.Date(string='Payment start date', required=True)
    ro_date = fields.Date(string='Date Added', default=fields.Date.today)
    ro_jop_position = fields.Many2one(related='ro_employee.job_id')
    ro_no_installments = fields.Float(string='No of installments',required=True,default=1.0)
    loan_line_ids = fields.One2many('loan.lines','loan_id')
    note = fields.Text(string='Note')
    total_paid_amount = fields.Monetary(compute='_compute_total_paid_amount')
    ro_balance = fields.Monetary(compute='_compute_balance')
    ro_loan_amount_total = fields.Monetary(related='ro_loan_amount')
    ro_loan_type = fields.Selection(string='Loan Type', selection=[('once', 'Once'), ('monthly', 'Monthly')],required=True,default='monthly')
   
    contract_ids = fields.One2many(
        'hr.contract', 'department_id', string='Contracts')
    ro_once_loan_amount = fields.Float(
        string='Once Loan Amount', compute='_compute_loan_amount')

    ro_monthly_loan_amount = fields.Float(
        string='Monthly Loan Amount', compute='_compute_loan_amount')
    

    state = fields.Selection([
        ('draft', 'Draft'),
        ('first_approve', 'First Approve'),
        ('second_approve', 'Second Approve'),
        ('paid', 'Paid'),
        ('refused', 'Refused'),
        
    ],
        required=True,
        default='draft',
        copy=False,
        # compute='_compute_state',
        store=True,
        tracking=True,
    )  
    

    @api.depends('ro_employee')
    def _compute_loan_amount(self):
        for loan in self:
            loan.ro_once_loan_amount = 0
            loan.ro_monthly_loan_amount = 0
            if loan.ro_employee.contract_id.state == 'open':
                if loan.ro_department.ro_once_loan_percentage:
                    loan.ro_once_loan_amount = (loan.ro_employee.contract_id.wage * loan.ro_department.ro_once_loan_percentage) / 100
                if loan.ro_department.ro_monthly_loan_percentage:
                    loan.ro_monthly_loan_amount = (loan.ro_employee.contract_id.wage * loan.ro_department.ro_monthly_loan_percentage) / 100
            else:
                pass
  
    @api.model
    def create(self, vals):
        
        sequence = self.env['ir.sequence'].next_by_code('Loan')
        vals['name'] = sequence or _('New')
        res = super(Loan, self).create(vals)
        return res

    def _compute_total_paid_amount(self):
        for loan in self:
            loan.total_paid_amount = sum(loan.loan_line_ids.filtered(lambda x: x.ro_paid).mapped('amount'))  
    def _compute_balance(self):
        for loan in self:
            loan.ro_balance = loan.ro_loan_amount - loan.total_paid_amount
          
    def create_loan_lines(self):
        # Remove existing loan lines
        self.loan_line_ids.unlink()

        if self.ro_no_installments != 0:
            installment_amount = self.ro_loan_amount / self.ro_no_installments
            loan_lines = []
            payment_date = self.ro_payment_start_date
            for i in range(int(self.ro_no_installments)):
                loan_lines.append((0, 0, {
                    'ro_payment_date': payment_date,
                    'amount': installment_amount,
                    'ro_paid': self.env.context.get('payslip').ro_paid if self.env.context.get('payslip') else False,

                }))
                payment_date = payment_date + relativedelta(months=1)  # Increment payment date by a month
            self.loan_line_ids = loan_lines
            
        else:
            pass


    def action_confirm(self):
        # employee_id = int(self.ro_employee)
        employee_id = self.ro_employee

        ro_payment_start_date_str = self.ro_payment_start_date
        # To handle loan amount
        if self.ro_employee.contract_id.state == 'open':
        
            if employee_id:
                # employee = self.env['hr.employee'].browse(employee_id.id)
                # if self.ro_loan_type == 'once':
                    
                #         if self.ro_once_loan_amount < (self.ro_loan_amount + employee.ro_loan_balance):
                #             employee.write({'ro_loan_balance': employee.ro_loan_balance})
                #             raise UserError("Cannot create a Once loan more than once loan precentage")
                        
                # if self.ro_loan_type == 'once':
                #     unpaid_loans = self.env['loan.lines'].search([
                #         ('loan_id.ro_employee', '=', employee_id.id),
                #         ('loan_id.state', '!=', 'paid'),
                #         ('loan_id.ro_loan_type', '=', 'once'),
                #     ])

                #     for unpaid_loan in unpaid_loans:
                #         if self.ro_payment_start_date.month == unpaid_loan.ro_payment_date.month or self.ro_payment_start_date.year != unpaid_loan.ro_payment_date.year:
                #             total_unpaid_amount = sum(loan.ro_loan_amount for loan in unpaid_loans)
                #             if self.ro_once_loan_amount < (self.ro_loan_amount + total_unpaid_amount):
                #                 raise UserError("Cannot create a Once loan more than once loan percentage")
                
                
                #--------------------problem of this way months tat doent have 31 days -----------------------
                # if self.ro_loan_type == 'once':
                #             unpaid_loans = self.env['hr.employee.loan'].search([
                #                 ('ro_employee', '=', employee_id.id),
                #                 ('state', '!=', 'paid'),
                #                 ('state', '!=', 'draft'),
                #                 ('ro_loan_type', '=', 'once'),
                #                 ('ro_payment_start_date', '>=', self.ro_payment_start_date.replace(day=1)),
                #                 ('ro_payment_start_date', '<=', self.ro_payment_start_date.replace(day=31)),
                #             ])
                #             total_unpaid_amount = sum(loan.ro_loan_amount for loan in unpaid_loans)
                #             if self.ro_once_loan_amount < (self.ro_loan_amount + total_unpaid_amount):
                #                 raise UserError("Cannot create a Once loan more than once loan percentage")
                            
                if self.ro_loan_type == 'once':
                    start_date = self.ro_payment_start_date.replace(day=1)
                    # Get the last day of the month
                    _, last_day = calendar.monthrange(self.ro_payment_start_date.year, self.ro_payment_start_date.month)
                    end_date = self.ro_payment_start_date.replace(day=last_day)

                    unpaid_loans = self.env['hr.employee.loan'].search([
                        ('ro_employee', '=', employee_id.id),
                        ('state', '!=', 'paid'),
                        ('state', '!=', 'draft'),
                        ('ro_loan_type', '=', 'once'),
                        ('ro_payment_start_date', '>=', start_date),
                        ('ro_payment_start_date', '<=', end_date),
                    ])
                    total_unpaid_amount = sum(loan.ro_loan_amount for loan in unpaid_loans)
                    if self.ro_once_loan_amount < (self.ro_loan_amount + total_unpaid_amount):
                        raise UserError("Cannot create a Once loan more than once loan percentage")
        
                else:
                    # if employee.department_id.ro_monthly_loan_percentage:
                        # if self.ro_monthly_loan_amount < (self.ro_loan_amount + employee.ro_loan_balance):
                        if self.ro_monthly_loan_amount < (self.ro_loan_amount):

                            # employee.write({'ro_loan_balance': employee.ro_loan_balance})
                            raise UserError("Cannot create a Monthly loan more than monthly loan precentage")

            # To handle loan date
            # if employee_id:
                
                # employee = self.env['hr.employee'].browse(employee_id.id)
                if self.ro_loan_type == 'monthly':
                    if self.ro_payment_start_date:
                        # employee_id = self.ro_employee
                        exist_loan = self.env['loan.lines'].search([
                            ('loan_id.ro_employee', '=', employee_id.id),
                            ('loan_id.state', '!=','draft' ),
                            ('loan_id.state', '!=','refused' ),
                            ('loan_id.ro_loan_type', '=','monthly' ),

                        ], order='ro_payment_date desc')
                        if exist_loan:
                            exist_loan = exist_loan[-1]
                            last_payment_date = exist_loan.ro_payment_date
                            if self.ro_payment_start_date.month == last_payment_date.month and self.ro_payment_start_date.year == last_payment_date.year:
                                raise UserError("Payment start date should be after the month of the last payment date for this employee. {}".format(exist_loan.loan_id.name))
                                               
                        
        else:
            raise UserError("employee's contract must be Running")

        if sum(self.loan_line_ids.mapped('amount')) != self.ro_loan_amount:
            raise UserError("Lines sum should be equal to the loan amount")

        # self.create_loan_lines()
        self.write({'state': 'first_approve'})
        

        
    def action_refused(self):
        
        self.write({'state': 'refused'})  

    def action_resttodraft(self):
        
        self.write({'state': 'draft'})        

    def action_submit_first_approve(self):
        self.state = 'first_approve'
        self.write({'state': 'second_approve'})

    
    
    def action_submit_second_approve(self):
        # Update state to 'second_approve'
        self.state = 'second_approve'    

        # Create cash entry in account.payment
        Payment = self.env['account.payment']


        payment = Payment.create({
            'payment_type': 'outbound',
            'partner_type': 'supplier',
            # 'partner_id':self.ro_employee.address_home_id.id,
            'partner_id': self.ro_employee.work_contact_id.id,
            'amount': self.ro_loan_amount,
            'date': self.ro_date,
            'is_document': True,
            'ref':self.name,
            
        })

       
        self.write({'state': 'paid'})
        
        

        return True

    def write(self, vals):
        if vals.get('ro_loan_type') == 'once':
           vals['ro_no_installments'] = 1
        
        for loan in self:
            if loan.state == 'paid' and not vals.get('state'):
                raise UserError("Cannot modify loan.")
        return super(Loan, self).write(vals)

   

    def unlink(self):
        for loan in self:
            if loan.state != 'draft':
                raise UserError("You can only delete loans in draft state.")
        return super(Loan, self).unlink()

  