from odoo import fields, models, api, _
from odoo.tools import float_compare, float_is_zero, plaintext2html

class Payslip(models.Model):
    _inherit = 'hr.payslip'

    ro_monthly_loans = fields.Float('Loans')
    ro_once_loan = fields.Float('Once Loans')
    loan_lines_ids = fields.Many2many('loan.lines', store=True)


    def compute_sheet(self):
        for rec in self:
            rec._onchange_employee_id()
        return super(Payslip, self).compute_sheet()

    @api.onchange('employee_id', 'date_from', 'date_to',)
    def _onchange_employee_id(self):
        if self.employee_id:


            monthly_loan_lines = self.env['loan.lines'].search([
                ('loan_id.ro_employee', '=', self.employee_id.id),
                ('ro_payment_date', '>=', self.date_from),
                ('ro_payment_date', '<=', self.date_to),
                ('ro_paid', '=', False),
                ('loan_id.state', '=','paid' ),
                ('loan_id.ro_loan_type', '=','monthly'),

            ])
            once_loan_lines = self.env['loan.lines'].search([
                ('loan_id.ro_employee', '=', self.employee_id.id),
                ('ro_payment_date', '>=', self.date_from),
                ('ro_payment_date', '<=', self.date_to),
                ('ro_paid', '=', False),
                ('loan_id.state', '=','paid' ),
                ('loan_id.ro_loan_type', '=','once'),

            ])
            
            self.ro_monthly_loans = 0
            self.ro_once_loan = 0    
            self.loan_lines_ids = [(5,0,0)]
            
            if monthly_loan_lines:
                total_loans = sum(loan.amount for loan in monthly_loan_lines)
                self.ro_monthly_loans = total_loans
                self.loan_lines_ids = [(4, line) for line in monthly_loan_lines.ids]
            
            if once_loan_lines:
                once_total_loans = sum(loan.amount for loan in once_loan_lines)
                self.ro_once_loan = once_total_loans
                self.loan_lines_ids = [(4, line) for line in once_loan_lines.ids]
            # if monthly_loan_lines:
            #     total_loans = sum(loan.amount for loan in monthly_loan_lines)
            #     self.ro_monthly_loans = total_loans
            #     self.loan_lines_ids = monthly_loan_lines.ids
            # else:
            #     if once_loan_lines:
            #         once_total_loans = sum(loan.amount for loan in once_loan_lines)
            #         self.ro_once_loan = once_total_loans
            #         self.loan_lines_ids = once_loan_lines.ids
            #     else:
            #         self.ro_monthly_loans = 0
            #         self.ro_once_loan = 0
            #         self.loan_lines_ids = False




    def action_payslip_paid(self):
        super(Payslip, self).action_payslip_paid()
        for payslip in self:
            if payslip.loan_lines_ids:
                for loan_line in payslip.loan_lines_ids:
                    if loan_line.loan_id.state == 'paid':
                        loan_line.write({
                            'ro_paid': True,
                            'ro_payslip_ref': payslip.number,
                        })


    def _prepare_slip_lines(self, date, line_ids):
        self.ensure_one()
        precision = self.env['decimal.precision'].precision_get('Payroll')
        new_lines = []
        for line in self.line_ids.filtered(lambda line: line.category_id):
            amount = line.total
            if line.code == 'NET': # Check if the line is the 'Net Salary'.
                for tmp_line in self.line_ids.filtered(lambda line: line.category_id):
                    if tmp_line.salary_rule_id.not_computed_in_net: # Check if the rule must be computed in the 'Net Salary' or not.
                        if amount > 0:
                            amount -= abs(tmp_line.total)
                        elif amount < 0:
                            amount += abs(tmp_line.total)
            if float_is_zero(amount, precision_digits=precision):
                continue
            debit_account_id = line.salary_rule_id.account_debit.id
            credit_account_id = line.salary_rule_id.account_credit.id
            if debit_account_id: # If the rule has a debit account.
                debit = amount if amount > 0.0 else 0.0
                credit = -amount if amount < 0.0 else 0.0

                debit_line = self._get_existing_lines(
                    line_ids + new_lines, line, debit_account_id, debit, credit)

                if not debit_line:
                    debit_line = self._prepare_line_values(line, debit_account_id, date, debit, credit)
                    debit_line['tax_ids'] = [(4, tax_id) for tax_id in line.salary_rule_id.account_debit.tax_ids.ids]
                    new_lines.append(debit_line)
                else:
                    if line.code == "Loans":
                        debit_line = self._prepare_line_values(line, debit_account_id, date, debit, credit)
                        debit_line['tax_ids'] = [(4, tax_id) for tax_id in line.salary_rule_id.account_debit.tax_ids.ids]
                        new_lines.append(debit_line)
                    else:
                        debit_line['debit'] += debit
                        debit_line['credit'] += credit

            if credit_account_id: # If the rule has a credit account.
                debit = -amount if amount < 0.0 else 0.0
                credit = amount if amount > 0.0 else 0.0
                credit_line = self._get_existing_lines(
                    line_ids + new_lines, line, credit_account_id, debit, credit)

                if not credit_line:
                    credit_line = self._prepare_line_values(line, credit_account_id, date, debit, credit)
                    credit_line['tax_ids'] = [(4, tax_id) for tax_id in line.salary_rule_id.account_credit.tax_ids.ids]
                    new_lines.append(credit_line)
                else:
                    if line.code == "Loans":
                        credit_line = self._prepare_line_values(line, credit_account_id, date, debit, credit)
                        credit_line['tax_ids'] = [(4, tax_id) for tax_id in line.salary_rule_id.account_credit.tax_ids.ids]
                        new_lines.append(credit_line)
                    else:
                        credit_line['debit'] += debit
                        credit_line['credit'] += credit
        return new_lines
    
    def _prepare_line_values(self, line, account_id, date, debit, credit):
        res = super(Payslip, self)._prepare_line_values(line, account_id, date, debit, credit)
        if line.code == "Loans":
            res['partner_id'] = self.employee_id.work_contact_id.id
        return res
