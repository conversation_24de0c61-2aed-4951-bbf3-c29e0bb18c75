<?xml version="1.0" encoding="utf-8"?>
<odoo>

      <record id="view_hr_payslip_inherit_add_fields" model="ir.ui.view">
            <field name="name">hr.payslip.view.inherit</field>
            <field name="model">hr.payslip</field>
            <field name="inherit_id" ref="hr_payroll.view_hr_payslip_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='struct_id']" position="after">
                     <field name="ro_monthly_loans" string="Monthly Loans" invisible="1"/> 
                     <field name="ro_once_loan" string=" Once Loans" invisible="1"/> 
                     <field name="loan_lines_ids" invisible="1" /> 

                     

                
                </xpath>
            </field>
        </record> 
<!-- # ahotoo fo2 -->

            <record id="ro_hr_salary_rule_ro_loans" model="hr.salary.rule">
            <field name="code">MLoans</field>
            <field name="name">Monthly Loans</field>
            <field name="category_id" ref="hr_payroll.DED" />
            <field name="struct_id" ref="hr_payroll.structure_002"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = True </field>

            <!-- .add condition  -->
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_monthly_loans</field>
            <field name="sequence" eval="31" />
            <field name="note">MLoans</field>
        </record>

         <record id="ro_hr_salary_rule_ro_loans_monthly" model="hr.salary.rule">
            <field name="code">OLoans</field>
            <field name="name">Once Loans</field>
            <field name="category_id" ref="hr_payroll.DED" />
            <field name="struct_id" ref="hr_payroll.structure_002" />
            <field name="condition_select">python</field>
            <field name="condition_python">result = True </field>

            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = -payslip.ro_once_loan</field>
            <field name="sequence" eval="31" />
            <field name="note">OLoans</field>
        </record>
        


        

</odoo>
