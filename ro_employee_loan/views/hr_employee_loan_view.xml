<?xml version="1.0" encoding="utf-8"?>

<odoo>
  <data>

  <record id="hr_employee_loan_tree_view" model="ir.ui.view">
            <field name="name">hr.loan.view.tree</field>
            <field name="model">hr.employee.loan</field>
            <field name="arch" type="xml">
                <tree string="employee loan">
                   
                    
                    <field name="name"/>
                    <field name="ro_employee"/>
                    <field name="state" widget="badge" select="1" readonly="1"/>


                    
                </tree>
            </field>
        </record>

         <record id="hr_employee_loan_search_view" model="ir.ui.view">
            <field name="name">hr.loan.view.search</field>
            <field name="model">hr.employee.loan</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Loan Name"/>
                    <field name="ro_employee" string="Employee"/>
                    <group expand="1" string="Group By">
                        <filter string="Loan Name" name="group_by_name" context="{'group_by':'name'}"/>
                        <filter string="Employee" name="group_by_employee" context="{'group_by':'ro_employee'}"/>
                    </group>
                </search>
            </field>
         </record>

     
      <record id="hr_laon_form_view" model="ir.ui.view">
            <field name="name">hr.loan.view.form</field>
            <field name="model">hr.employee.loan</field>
            <field name="arch" type="xml">
            <form string="Loan">
            <header>
              <!-- <button name="create_loan_lines" string="Compute" type="object" attrs="{'invisible':[('state', 'not in', 'draft')]}"/> -->
              <button name="create_loan_lines" string="Compute" type="object" invisible="state != 'draft'"/>
              <!-- <button name="action_confirm" string="Confirm" type="object" attrs="{'invisible':[('state', 'not in', 'draft')]}"/> -->
              <button name="action_confirm" string="Confirm" type="object" invisible="state != 'draft'"/>
              <button name="action_resttodraft" string="Reset to draft" type="object" invisible="state in ['draft', 'paid']"/>

              <button name="action_submit_first_approve" string="First Approve" type="object" invisible ="state != 'first_approve'" groups="ro_employee_loan.first_approve_group"/>


              <!-- <button name="action_submit_first_approve" string="First Approve" type="object" attrs="{'invisible':[('state', 'not in', 'first_approve')]}" groups="ro_employee_loan.first_approve_group"/> -->
              <!-- <button name="action_submit_second_approve" string="Second Approve" type="object" attrs="{'invisible':[('state', 'not in', 'second_approve')]}" groups="ro_employee_loan.second_approve_group"/> -->
              <button name="action_submit_second_approve" string="Second Approve" type="object" invisible="state != 'second_approve'" groups="ro_employee_loan.second_approve_group"/>

              <!-- <button name="action_refused" string="Refused" type="object" attrs="{'invisible':[('state', 'not in', 'second_approve')]}"/> -->
              <button name="action_refused" string="Refused" type="object" invisible ="state != 'second_approve'"/>

              <field name="state" widget="statusbar" select="1" readonly="1"
                           />
            </header>               

                <sheet>            
                        <div class="oe_title">
                              <h1>
                                  
                                <field name="name" readonly="1"/>
                              </h1>        
                        </div>
                     <group>
                             
                                <group name="loan">
                                 
                                  
                                    <!-- <field name="ro_employee" attrs="{'readonly': [('state', '=', 'second_approve')]}" /> -->
                                    <field name="ro_employee" readonly ="state == 'second_approve'" />

                                    <!-- <field name="ro_department" attrs="{'readonly': [('state', '=', 'second_approve')]}" readonly="1"/>    -->
                                    <field name="ro_department"  readonly="1"/>   

                                    <field name="ro_loan_amount" force_save="1" readonly="state == 'second_approve'"/>     
                                    <field name="ro_payment_start_date" readonly="state == 'second_approve'"/>
                                    <field name="ro_loan_type" readonly ="state != 'draft'"/>
                                 
                                </group>
                                <group name="loan">
                                    <field name="ro_date" readonly="state == 'second_approve'"/>
                                    <field name="ro_jop_position" readonly="1"/>
                                    <field name="company_id" invisible="1"/>
                                    <!-- <field name="ro_no_installments" attrs="{'readonly': ['|', ('state', '=', 'second_approve'), ('ro_loan_type', '=', 'once')]}" /> -->
                                    <field name="ro_no_installments" readonly="state == 'second_approve'"  invisible ="ro_loan_type == 'once'"/>

                                    <field name="ro_once_loan_amount" invisible="1"/>
                                    <field name="ro_monthly_loan_amount" invisible="1"/>
                                </group>
                               
                   </group>
              


            <notebook>
 
                <page name="installments" string="Installments">
                    <field name="loan_line_ids" widget="one2many" context="{'show_attribute': False}" readonly="state == 'second_approve'">
                        <tree string="Loans" editable="bottom">
                            <field name="ro_payment_date"/>
                            <field name="amount"/>
                            <field name="ro_paid"/>
                            <field name="ro_payslip_ref"/>       
                        </tree>
                    </field>      
                </page>


               </notebook>
                <group name="note_group" col="6" class="mt-2 mt-md-0">
                            <group colspan="4">
                                <field colspan="2" name="note" nolabel="1" placeholder="Terms and conditions..."/>
                            </group>
                            <group class="oe_subtotal_footer oe_right" colspan="2" name="sale_total">
                                <field name="ro_loan_amount_total" string="Amount" colspan="2" readonly="1"/>
                                <field name="total_paid_amount" string="Total Paid" colspan="2" readonly="1"/>
                                <field name="ro_balance" string="balance" colspan="2" readonly="1"/>
                            </group>
                            <div class="clearfix"/>
                        </group>
                  </sheet>
              </form>
            </field>
      </record>

                      
      <record id="hr_employee_loan_action" model="ir.actions.act_window">
            <field name="name">Loans</field>
            <field name="res_model">hr.employee.loan</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="ro_employee_loan.hr_employee_loan_search_view"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Loan
                </p>
               
            </field>
        </record>
      <menuitem
            id="menu_hr_Loans"
            name="Loans"
            action="hr_employee_loan_action"
            parent="hr.menu_hr_root"
            sequence="120"
            />

                 

           
  </data>
</odoo>