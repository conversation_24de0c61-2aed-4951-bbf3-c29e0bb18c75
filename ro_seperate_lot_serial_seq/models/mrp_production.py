from odoo import models,_
from odoo.exceptions import UserError

class MrpProduction(models.Model):
    _inherit = 'mrp.production'
    
    def _set_lot_producing(self):
        self.ensure_one()
        if self.product_id.tracking == 'lot':
            super()._set_lot_producing()
        elif self.product_id.tracking == 'serial':
            self.lot_producing_id = self.env['stock.lot'].create(self._prepare_stock_serial_number_values())
        
        
    def _prepare_stock_serial_number_values(self):
        self.ensure_one()
        name = self.env['ir.sequence'].next_by_code('stock.serial.number')
        exist_lot = not name or self.env['stock.lot'].search([
            ('product_id', '=', self.product_id.id),
            ('company_id', '=', self.company_id.id),
            ('name', '=', name),
        ], limit=1)
        if exist_lot:
            name = self.env['stock.lot']._get_next_serial(self.company_id, self.product_id)
        if not name:
            raise UserError(_("Please set the first Serial Number or a default sequence"))
        return {
            'product_id': self.product_id.id,
            'company_id': self.company_id.id,
            'name': name,
        }