<odoo>
    <record id="view_order_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.inherit</field>
        <field name="model">sale.order</field>
        <field name="priority">17</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="ro_is_customer" invisible="1"/>
                <field name="ro_country" invisible="1"/>
            </xpath> 
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="domain">
                [('company_id', 'in', (False, company_id)),('ro_is_customer', '=', True),('country_id', '=', ro_country)]
                <!-- [('company_id', 'in', (False, company_id)),'|',('ro_is_customer', '=', True),('country_id', '=', ro_country)] -->

                </attribute>
            </xpath>
            
          
        </field>
    </record>

    <record id="sale_order_logistics_fields_form_view_inherited" model="ir.ui.view">
        <field name="name">sale.order.logistics.inherited</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="ro_logistics.sale_order_logistics_fields_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='logistics']//field[@name='agent_id']" position="after">
                <field name="ro_is_agent" invisible="1"/>
            </xpath> 
            <xpath expr="//page[@name='logistics']//field[@name='agent_id']" position="attributes">
                <attribute name="domain">
                [('ro_is_agent', '=', True)]

                </attribute>
            </xpath>
            
          
        </field>
    </record>
</odoo>