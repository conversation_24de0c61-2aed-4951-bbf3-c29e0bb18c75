from odoo import models, fields, api, _


# Customer & Agent
class SaleOrder(models.Model):
    _inherit = 'sale.order'
    ro_is_customer= fields.Boolean(related='partner_id.ro_is_customer')
    ro_is_agent= fields.Boolean(related='partner_id.ro_is_agent')
    ro_country=fields.Many2one('res.country','Country')
    
    
    
class CrmLead(models.Model):
    _inherit='crm.lead'
    ro_is_customer= fields.Boolean(related='partner_id.ro_is_customer')
    
    partner_id = fields.Many2one(
        'res.partner', domain=[], string='Customer', check_company=True, index=True, tracking=10,
        help="Linked partner (optional). Usually created when converting the lead. You can find a partner by its Name, TIN, Email or Internal Reference.")    
    
# Vendor
class PurchaseOrder(models.Model):
    _inherit='purchase.order'
#     ro_is_vendor= fields.Boolean(related='partner_id.ro_is_vendor')
    
    partner_id = fields.Many2one('res.partner', domain="[('ro_is_vendor', '=', True), '|', ('company_id', '=', False), ('company_id', '=', company_id)]")

# Employee
# class AccountMove(models.Model):
#     _inherit='account.move'
#     ro_is_employee= fields.Boolean(related='partner_id.ro_is_employee')
    
# class AccountPayment(models.Model):
#     _inherit='account.payment'
#     ro_is_employee= fields.Boolean(related='partner_id.ro_is_employee')
    
    
    

    
    
        
        
    