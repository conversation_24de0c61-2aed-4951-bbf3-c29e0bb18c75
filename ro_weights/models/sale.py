from odoo import models, fields, api

class SaleOrderLineCustom(models.Model):
    _inherit = 'sale.order.line'


    ro_computed_gross_weight = fields.Float(
        string='Computed Gross Weight',
        compute='_compute_product_gross_weight',
        readonly=False,
        store=True
        
        )
    ro_computed_net_weight = fields.Float(
        string='Computed Net Weight', 
        compute='_compute_product_net_weight', 
        readonly=False,
        store=True
        
        )
    

    @api.onchange('product_id','product_uom_qty', 'product_id.ro_gross_weight')
    def _compute_product_gross_weight(self):
        for line in self:
            line.ro_computed_gross_weight = line.product_uom_qty * line.product_id.ro_gross_weight
   
    @api.onchange('product_id','product_uom_qty', 'product_id.weight')
    def _compute_product_net_weight(self):
        for line in self:
            line.ro_computed_net_weight = line.product_uom_qty * line.product_id.weight
        
   



class SaleOrderCustom(models.Model):
    _inherit = 'sale.order'


    total_ro_computed_gross_weight = fields.Float(
        string='Total Gross Weight',
        compute='_compute_total_gross_weight',
        readonly=True,
        store=True
        
        )

    total_ro_computed_net_weight = fields.Float(
        string='Total Net Weight',
        compute='_compute_total_net_weight',
        readonly=True,
        store=True
        
        )
    @api.depends('order_line.ro_computed_gross_weight')
    def _compute_total_gross_weight(self):
        for order in self:
            order.total_ro_computed_gross_weight = sum(order.order_line.mapped('ro_computed_gross_weight'))

    @api.depends('order_line.ro_computed_net_weight')
    def _compute_total_net_weight(self):
        for order in self:
            order.total_ro_computed_net_weight = sum(order.order_line.mapped('ro_computed_net_weight'))
    





