from odoo import fields, models, tools

class HrEmployeeInfoReport(models.BaseModel):
    _auto = False
    _name = 'hr.employee.info.report'
    _description = 'Employee Information Report'

    id = fields.Id()
    display_name = fields.Char(related='employee_id.name')
    employee_id = fields.Many2one('hr.employee', readonly=True)
    department_id = fields.Many2one('hr.department', readonly=True)
    yes_or_no = fields.Char('yes or no ')
    field = fields.Char('Field Name')

    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)

        # Get the fields of hr.employee model
        employee_fields = {'ro_copy_national_id': "Copy National",'ro_birth_certificate':"Birth certificate", 'ro_graduation_certificate':"Graduation certificate", 'ro_military_certificate':"Military Certificate", 'ro_personal_photo':"Personal Photo",'ro_criminal_record':"Criminal Record",'ro_form':"Form 111",'ro_social_insturance_print':"Social Insturance Print",'ro_labour_office_paper':"Labour Office Paper",'ro_offer_letter':"Offer Letter",'ro_contract':"Contract",'ro_confidentiality':"Confidentiality Agreement",'ro_social_insturance_axa':"Social Insturance Axa",'ro_interview_form':"Interview Evaluatio Form",'ro_renewal_contract':"Renewal Contrac",'ro_labor_office_receiving':"Labor office receiving",'ro_date_contract':"Date of Contract",'ro_receiving_items':"Receiving Items"}
        # , 'ro_birth_certificate', 'ro_graduation_certificate', 'ro_military_certificate', 'ro_personal_photo','ro_criminal_record','ro_social_insturance_print','ro_labour_office_paper','ro_offer_letter','ro_contract','ro_confidentiality','ro_social_insturance_axa','ro_interview_form',

        # Calculate the total number of employees
        total_employees = self.env['hr.employee'].search_count([])

        # Create the SELECT statements dynamically using a for loop and enumerate
        select_statements = []
        for index, field in enumerate(employee_fields):
            
           
            select_statements.append(
                f"SELECT ({total_employees} * {index + 1}) + e.id AS id, "
                f"e.id AS employee_id, "
                f"e.department_id AS department_id, "
                f"e.{field} AS yes_or_no, "
                f"'{employee_fields[field]}' AS field "
                f"FROM {self.env['hr.employee']._table} e"
            )
            # select_statements.append(
            #     f"SELECT ({total_employees} * {index + 1}) + e.id AS id, "
            #     f"e.id AS employee_id, "
            #     f"e.department_id AS department_id, "
            #     f"e.{list(field.keys())} AS yes_or_no, "
            #     f"'{list(field.values())}' AS field "
            #     f"FROM {self.env['hr.employee']._table} e"
            # )

        # Combine the SELECT statements using UNION
        select_statement = " UNION ".join(select_statements)

        # Create the view using the dynamically generated SELECT statement
        self.env.cr.execute(f"""
            CREATE OR REPLACE VIEW {self._table} AS (
                {select_statement}
            )
        """)

