from odoo import models, fields, api, _

class HrPayslip(models.Model):
    _inherit = 'hr.payslip'


    ro_social_insurance = fields.Float(compute='_compute_rules', store=True,string="تأمينات اجتماعية")
    ro_family_discount_returns = fields.Float(compute='_compute_rules', store=True,string="تأمينات اجتماعية")



    @api.depends('line_ids.total')
    def _compute_rules(self):
        line_values = (self._origin)._get_line_values(['SOCIAL','FAMILYMED'])
        for payslip in self:
                    payslip.ro_social_insurance = line_values['SOCIAL'][payslip._origin.id]['total']
                    payslip.ro_family_discount_returns = line_values['FAMILYMED'][payslip._origin.id]['total']
                    # payslip.net_wage = line_values['NET'][payslip._origin.id]['total']

    
