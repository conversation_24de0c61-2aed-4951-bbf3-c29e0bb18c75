<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="paperformat_survey_certification_job" model="report.paperformat">
       <field name="name">Job order paperformat</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">50</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">10</field>
            <field name="margin_right">10</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">40</field>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">110</field>
    </record>



    <template id="job_external_layout">

        <div t-attf-class="header" t-att-style="report_header_style">
            <div class="row">

                <!-- <div class="col-3 mb4"> -->
                <div>
                    <center>
                        <h3>Job Order</h3>
                    </center>
                    <img t-att-src="'ro_so_print//static/src/img/logoh.png'" alt="Company Logo" style="float: left; margin-right: 10px;"/>
                </div>

            </div>

        </div>

        <!-- <div t-attf-class="article o_report_layout_standard o_company_#{o.company_id.id}_layout {{  'o_report_layout_background' if o.company_id.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % o.company_id.layout_background_image.decode('utf-8') if o.company_id.layout_background_image and o.company_id.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if o.company_id.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
         -->
        <div t-attf-class="article o_report_layout_standard " t-attf-style="background-image: url('/ro_so_print/static/src/img/water_mark.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
            <div class="pt-5">

                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
        </div>
       
       
        <!-- <div t-attf-class="footer" style="position: relative; z-index: 100;">
                                <div class="row">
                                  
                                   
                                    
                             </div>
                    <div class="text-center" style="border-top: 1px solid black;">
                    
                  
                
                         
                   


                    <div style="margin-top:30px;">
                    <b> <p style="text-align:center;">Egyptian Tax Registration Number: ***********</p></b>
                        
                    
                       </div>    

            <div class="row">
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_so_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>
                                    
                                    <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color: blue; text-decoration: underline;">
                                            <EMAIL>
                                    </a>
                                    
                                    </div>
                                   
                                    
                                     <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_so_print//static/src/img/world-wide-web.png'" alt="email" style="height:25px;wedth:25px;"/>

                                      <a t-att-href="'http://www.xeedcorp.com/'" style="color: blue; text-decoration: underline;">
                                                            www.xeedcorp.com
                                     </a>
                                    </div>
                                   
                                    
                                     <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_so_print//static/src/img/telephone.png'" alt="email" style="height:25px;wedth:25px;"/>

                                     <strong><span style="color: blue;">+2010 915 56 556</span></strong>
                                    </div>
            </div>
            </div>
        </div> -->
    </template>

    <record id="action_job_order_report" model="ir.actions.report">
        <field name="name">Job Order</field>
        <field name="model">sale.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ro_so_print.job_report_template</field>
        <field name="report_file">ro_so_print.job_report_template</field>
        <field name="paperformat_id" ref="ro_so_print.paperformat_survey_certification_job"/>
        <field name="print_report_name">'JOb Order'</field>
        <field name="binding_model_id" ref="sale.model_sale_order"/>
        <field name="binding_type">report</field>
    </record>


    <template id="job_report_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="ro_so_print.job_external_layout">
                    <div class="page">
                        <div class="row" style="width: 100%; display: flex; justify-content: space-between;">
                            <style>
                                    .no-border tbody{border:0px !important;}
                            </style>
                            <div class="col-9" style="text-align: left;">
                                <strong>
                                    <p>XEED FOR IMPORT AND EXPORT</p>
                                </strong>
                                <p style="font-size=14px;">Building No. 37, Walk of Cairo Trade Center,</p>
                                <p style="font-size=14px;">Kilo 38, Cairo-Alexandria Desert Road -Sheikh Zayed,Egypt</p>

                            </div>
                            <div class="col-3" style="text-align: right; margin-right:70px;font-size:13px;">
                                <table class="no-border" style="border-collapse: collapse; width: 100%;  direction: rtl;margin-right:32px;">
                                    <tr style="border: none;">
                                        <td style="border: none; direction: ltr;">
                                            <p style=" text-align:left;break-word;max-width:200px; ">

                                                <b> Job Order No : </b>
                                                <span t-field="o.name" style=" direction:ltr;"></span>

                                            </p>
                                        </td>
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none; direction: ltr;">
                                            <p style=" text-align:left; break-word;max-width:200px;">

                                                <b> Date : </b>
                                                <span t-field="o.date_order" style=" direction:ltr;"></span>

                                            </p>
                                        </td>
                                    </tr>


                                </table>
                            </div>
                        </div>

                        <b>
                            <hr style="border: 1px solid #000; width:100%;font-weight: bold;"/>
                        </b>
                        <div class="row" style="width: 100%; display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <div class="col-9" style="text-align: left;">
                                <table class="no-border" style="border-collapse: collapse; width: 70;  direction: rtl; font-size:14px;">
                                    <tr style="border: none;">
                                        <td style="border: none; direction: ltr;">
                                            <p style=" text-align:left;">

                                                <b> Client :</b>
                                                <span t-field="o.partner_id.name" style=" direction:ltr;"></span>


                                            </p>
                                        </td>

                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none; direction: ltr;">
                                            <div style=" text-align:left;">
                                                <b> Address : </b>
                                                <span t-field="o.partner_id.street" style=" direction:ltr;"/>
                                ,         <span t-field="o.partner_id.street2" style=" direction:ltr;"/>
                                ,          <div>
                                        <span t-field="o.partner_id.city" style=" direction:ltr;"/>
                                &#160;<span t-field="o.partner_id.state_id.code" style=" direction:ltr;"/>
                                &#160;<span t-field="o.partner_id.zip" style=" direction:ltr;"/>
                                ,             <span t-field="o.partner_id.country_id" style=" direction:ltr;"/>
                                        </div>

                                        <!-- <b> Address :</b>  <span t-field="o.partner_id.street" style=" direction:ltr;"/> <div t-field="o.partner_id.street2" style=" direction:ltr;padding-left:60px;"/><span t-field="o.partner_id.city" style=" direction:ltr;padding-left:60px;"/>&#160;<span t-field="o.partner_id.state_id.code" style=" direction:ltr;"/>&#160;<span t-field="o.partner_id.zip" style=" direction:ltr;"/><p t-field="o.partner_id.country_id" style=" direction:ltr;padding-left:60px;"/> -->



                                    </div>
                                </td>

                            </tr>

                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;padding-top:10px;">
                                    <p style=" text-align:left;">

                                        <b>Consignee :</b>
                                        <span t-field="o.consignee_id" style=" direction:ltr;"/>


                                    </p>
                                </td>

                            </tr>
                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style=" text-align:left;">

                                        <b>Notify :</b>
                                        <span t-field="o.notify_id" style=" direction:ltr;"/>


                                    </p>
                                </td>

                            </tr>
                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style=" text-align:left;">

                                        <b>Sec Notify :</b>
                                        <span t-field="o.sec_notify_id" style=" direction:ltr;"/>


                                    </p>
                                </td>

                            </tr>
                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style=" text-align:left; ">
                                        <b>
                                                    Country of Origin :
                                        </b> Egypt
                                                                                            <!-- Invoice Date :<span t-field="o.invoice_date" style=" direction:ltr;"></span>  -->

                                    </p>
                                </td>
                            </tr>

                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style=" text-align:left;">

                                        <b> Port of Departure :</b>
                                        <span t-field="o.loading_port" style=" direction:ltr;"></span>

                                    </p>
                                </td>
                            </tr>

                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style="text-align:left;">
                                        <b>  No. of Pallets :</b>


                                        <!-- Due Date :<span t-field="o.invoice_date_due" style=" direction:ltr;"></span>  -->

                                    </p>
                                </td>
                            </tr>
                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style="text-align:left;">


                                        <b> Loading Date :</b>
                                        <span t-field="o.loading_date" style="direction:ltr;"></span>

                                    </p>
                                </td>
                            </tr>
                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style="text-align:left;">
                                        <b> Gross Weight :</b>
                                        <span t-field="o.total_ro_computed_gross_weight" style=" direction:ltr;"></span>
                                        <span style="padding-left:5px;">KG</span>


                                        <!-- Due Date :<span t-field="o.invoice_date_due" style=" direction:ltr;"></span>  -->

                                    </p>
                                </td>
                            </tr>







                        </table>
                    </div>

                    <div class="col-3" style="text-align: right; margin-right:70px;font-size:14px;">
                        <table class="no-border" style="border-collapse: collapse; width: 100%;  direction: rtl;">
                            <!-- <tr style="border: none;">
                                            <td style="border: none; direction: ltr;">
                                                <p style=" text-align:left;">
                                                                                              

                                                
                                                </p>
                                            </td>
                                            </tr> -->

                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <p style=" text-align:left;break-word;max-width:200px;">


                                        <b> Destination :</b>
                                        <span t-field="o.partner_shipping_id" style="direction:ltr;"></span>

                                    </p>
                                </td>
                            </tr>
                            <tr style="border: none;">
                                <td style="border: none; direction: ltr;">
                                    <div style=" text-align:left;">
                                        <b> Address : </b>
                                        <span t-field="o.partner_shipping_id.street" style=" direction:ltr;"/>
                                                ,                                        <span t-field="o.partner_shipping_id.street2" style=" direction:ltr;"/>
                                                ,                                        <div>
                                                                                            <span t-field="o.partner_shipping_id.city" style=" direction:ltr;"/>
                                                &#160;<span t-field="o.partner_shipping_id.state_id.code" style=" direction:ltr;"/>
                                                &#160;<span t-field="o.partner_shipping_id.zip" style=" direction:ltr;"/>
                                                ,                                    <span t-field="o.partner_shipping_id.country_id" style=" direction:ltr;"/>
                                                                                </div>

                                <!-- <b> Address :</b>  <span t-field="o.partner_id.street" style=" direction:ltr;"/> <div t-field="o.partner_id.street2" style=" direction:ltr;padding-left:60px;"/><span t-field="o.partner_id.city" style=" direction:ltr;padding-left:60px;"/>&#160;<span t-field="o.partner_id.state_id.code" style=" direction:ltr;"/>&#160;<span t-field="o.partner_id.zip" style=" direction:ltr;"/><p t-field="o.partner_id.country_id" style=" direction:ltr;padding-left:60px;"/> -->



                            </div>
                            <!-- <p style=" text-align:left;break-word;max-width:200px;">
                                                 
                                                
                                                     <b> Address : </b> <span t-esc="o.partner_shipping_id.street" style=" direction:ltr;"/>,<p t-esc="o.partner_shipping_id.street" style=" direction:ltr;border:0px;margin0px"/>,<span t-field="o.partner_shipping_id.city" style=" direction:ltr;"/>&#160;<span t-field="o.partner_shipping_id.state_id.code" style=" direction:ltr;"/>&#160;<span t-field="o.partner_shipping_id.zip" style=" direction:ltr;"/>,<span t-field="o.partner_shipping_id.country_id" style=" direction:ltr;"/> -->
                            <!-- <b> Address :</b>  <span t-field="o.partner_shipping_id.street" style=" direction:ltr;"/> <div t-field="o.partner_shipping_id.street2" style=" direction:ltr;"/><span t-field="o.partner_shipping_id.city" style=" direction:ltr;"/>&#160;<span t-field="o.partner_shipping_id.state_id.code" style=" direction:ltr;"/>&#160;<span t-field="o.partner_shipping_id.zip" style=" direction:ltr;"/><p t-field="o.partner_shipping_id.country_id" style=" direction:ltr;"/> -->


                            <!-- </p> -->
                        </td>
                    </tr>

                    <tr style="border: none;">
                        <td style="border: none; direction: ltr;padding-top:10px;">
                            <p style=" text-align:left;break-word;max-width:200px;">

                                <b>Port of Arrivial: </b>
                                <span t-field="o.destination_port" style=" direction:ltr;"></span>

                            </p>
                        </td>
                    </tr>
                    <tr style="border: none;">
                        <td style="border: none; direction: ltr;">
                            <p style="text-align:left;break-word;max-width:200px;">

                                <b>Shipping Method : </b>
                                <span t-field="o.shipping_method_id" style=" direction:ltr;"></span>

                            </p>
                        </td>
                    </tr>
                    <tr style="border: none;">
                        <td style="border: none; direction: ltr;">
                            <p style="text-align:left;break-word;max-width:200px;">
                                <b> Incoterm :</b>
                                <span t-field="o.incoterm.code" style=" direction:ltr;"></span>

                            </p>
                        </td>
                    </tr>

                    <tr style="border: none;">
                        <td style="border: none; direction: ltr;">
                            <p style="text-align:left;break-word;max-width:200px;">
                                <b>  Net Weight : </b>
                                <span t-field="o.total_ro_computed_net_weight" style=" direction:ltr;"></span>
                                <span style="padding-left:5px;">KG</span>
                                <!-- Term of Delivery :<span t-field="o.invoice_incoterm_id" style=" direction:ltr;"></span>  -->

                            </p>
                        </td>
                    </tr>



                </table>
            </div>
        </div>

        <t t-set="filtered_lines" t-value="o.order_line.filtered(lambda l: not l.display_type)"/>

        <table class="text-center" style="border-collapse: collapse; width: 100%;  direction: ltr;">
            <thead>
                <tr>

                    <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">Product</th>
                    <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">Variety</th>
                    <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Package</th> -->
                    <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Size </th>
                   
                    <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Carton Type </th>
                    <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">
                        <t t-if="filtered_lines and filtered_lines[0].product_uom">
                            <t t-esc="filtered_lines[0].product_uom.name or 'Package'"/>
                        </t>
                        <t t-else="">
                                            Package
                        </t>
                    </th>
                    <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">No.of Cartons</th> -->
                    <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">No.of Containers</th> -->







                </tr>

            </thead>

            <tbody>

                <!-- <t t-foreach="o.order_line" t-as="line"> -->
                <t t-foreach="filtered_lines" t-as="line">


                    <tr>
                        <td style="border: 1px solid #000; padding: 8px;text-align:center;">
                            <t t-esc="line.product_template_id.name"/>
                        </td>
                        <td style="border: 1px solid #000; padding: 8px;text-align:center;">
                            <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                <t t-if="attribute_line.attribute_id.ro_variety">
                                    <t t-esc="attribute_line.name"/>
                                </t>
                            </t>
                        </td>
                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                            <t t-foreach="line.sale_product_tag_ids" t-as="tag">
                                <span t-esc="tag.name"/>,
                            </t>
                        </td>
                        <!-- <td style="border: 1px solid #000; padding: 8px;text-align:center;"><t t-esc="line.sale_product_tag_ids.name"/></td> -->
                          
                        <!-- <t t-if="line.product_id.product_template_attribute_value_ids.attribute_id">
                            <td style="border: 1px solid #000; padding: 8px;text-align:center;">
                                <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                    <t t-if="attribute_line.attribute_id.ro_size">
                                        <t t-esc="attribute_line.name"/>
                                    </t>
                                </t>

                            </td>
                        </t>
                        <t t-else="">
                            <td style="border: 1px solid #000; padding: 8px;text-align:center;"></td>
                        </t> -->


                        <!-- <t t-if="line.product_id.product_template_attribute_value_ids.attribute_id">
                            <td style="border: 1px solid #000; padding: 8px;text-align:center;">
                                <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                    <t t-if="attribute_line.attribute_id.ro_carton">
                                        <t t-esc="attribute_line.name"/>
                                    </t>
                                </t>

                            </td>
                        </t>
                        <t t-else="">
                            <td style="border: 1px solid #000; padding: 8px;text-align:center;"></td>
                        </t> -->
                        <!-- to get carton color only  -->
                        <td style="border: 1px solid #000; padding: 8px;text-align:center;"><t t-esc="line.attr_2"/></td>

                        <td style="border: 1px solid #000; padding: 8px;text-align:center;">
                            <!-- <t t-esc="line.product_packaging_id.name"/> -->
                            <t t-esc="line.product_uom_qty"/>
                        </td>

                        <!-- <td style="border: 1px solid #000; padding: 8px;text-align:center;">
                            <t t-esc="line.product_uom_qty"/>
                        </td> -->
                        <!-- <td style="border: 1px solid #000; padding: 8px;text-align:center;"><t t-esc="line.ro_no_container"/></td> -->






                    </tr>
                </t>

                <tr>
                    <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="4">Total
                    </td>
                    <td style="border: 1px solid #000; padding: 8px;text-align:center;" colspan="1">
                        <t t-esc="sum(o.order_line.mapped('product_uom_qty'))"/>
                    </td>
                    <!-- <td style="border: 1px solid #000; padding: 8px;text-align:center;" colspan="1"><t t-esc="sum(o.order_line.mapped('ro_no_container'))"/></td> -->

                </tr>

            </tbody>

        </table>

        <p style="text-align:left;">
            <u>
                <b>Paymen term:</b>
            </u>
            <span t-field="o.payment_term_id" style="direction:ltr;"></span>
        </p>

        <table style="border: 1px solid #000; width:100%;">


            <tr>


                <p style="text-align:left;">
                    <b>Notes</b>
                </p>
                <td style="border: 1px solid #000; padding: 8px;text-align:left;">
                    <p>
                        <b>if you have any update in consignee and notify</b>
                    </p>
                    <span t-field="o.note" style="direction:ltr;"/>
                </td>
            </tr>
        </table>


        <table class="no-border" style="border: 1px solid #000; width:100%;">

            <tr>

                <div class="text-center" style="border-top: 1px solid black;margin-top:10px;">

                    <div style="padding:10px;">
                        <b>
                            <div style="text-align:center;">Egyptian Tax Registration Number: ***********</div>
                        </b>


                    </div>

                    <div class="row">
                        <div class="col-4" style="text-align:center;">
                            <img t-att-src="'ro_so_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>

                            <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color: blue; text-decoration: underline;">
                                            <EMAIL>
                            </a>

                        </div>


                        <div class="col-4" style="text-align:center;">
                            <img t-att-src="'ro_so_print//static/src/img/world-wide-web.png'" alt="email" style="height:25px;wedth:25px;"/>

                            <a t-att-href="'http://www.xeedcorp.com/'" style="color: blue; text-decoration: underline;">
                                                            www.xeedcorp.com
                            </a>
                        </div>


                        <div class="col-4" style="text-align:center;">
                            <img t-att-src="'ro_so_print//static/src/img/telephone.png'" alt="email" style="height:25px;wedth:25px;"/>

                            <strong>
                                <span style="color: blue;">+2010 915 56 556</span>
                            </strong>
                        </div>
                    </div>
                </div>


            </tr>

        </table>



        <!-- <div style="margin-top:10px;">
                         <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                    </div>   
                         -->





        <!-- <div class="oe_structure"></div>
                        <p style="page-break-before:always;"></p> -->






        <!-- </div> -->

        <!-- <div class="oe_structure"></div>
                        <p style="page-break-before:always;"></p> -->

    </div>
</t>
</t>
</t>

</template>

</odoo>