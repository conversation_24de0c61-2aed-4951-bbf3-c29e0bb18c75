# -*- coding: utf-8 -*-
{
    'name': "sale order print",

    'summary': "so print",

    'description': """
       so print
    """,

    'author': "Roaya",
    'website': "https://www.roayadm.com",
    'category': 'sale',
    'version': '17.0',

    # any module necessary for this one to work correctly
    'depends': ['base','sale','ro_product_attributes_fields','ro_logistics','ro_weights','sale'],

    # always loaded
    'data': [
       
        'views/job_order.xml',
        'views/sale_order_view.xml',
        'views/so_templates.xml',
    ],
    'installable': True,
    'license': 'OPL-1',
    
}

