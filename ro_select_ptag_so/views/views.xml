<odoo>
  <data>

    <record id="edit_in_view_order_line_tree_inherit" model="ir.ui.view">
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale.view_order_form"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='order_line']//tree//field[@name='product_uom_qty']" position="after">
          <field name="available_tag_ids" column_invisible="1"/>
          <field name="sale_product_tag_ids" widget="many2many_tags" options="{'color_field': 'ro_color'}"/>
        </xpath>
      </field>
    </record>

    <record id="edit_in_view_move_line_tree_inherit" model="ir.ui.view">
      <field name="model">stock.picking</field>
      <field name="inherit_id" ref="stock.view_picking_form"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='move_ids_without_package']/tree/field[@name='product_uom_qty']" position="after">
          <field name="stock_product_tag_ids" widget="many2many_tags" options="{'color_field': 'ro_color'}"/>
        </xpath>
      </field>
    </record>

    <record id="view_mrp_production_form_inherit" model="ir.ui.view">
      <field name="model">mrp.production</field>
      <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='product_id']" position="after">
          <field name="mo_product_tag_ids" widget="many2many_tags" options="{'color_field': 'ro_color'}"/>
        </xpath>
      </field>
    </record>

  </data>
</odoo>
