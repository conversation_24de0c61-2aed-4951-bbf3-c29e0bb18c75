from odoo import models, fields, api

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    mo_product_tag_ids = fields.Many2many(
        'product.tag', string='Product Tag', readonly=True
    )
    def _generate_moves(self):
        super(MrpProduction, self)._generate_moves()
        for production in self:
            if production.origin:
                sale_order = self.env['sale.order'].search([('name', '=', production.origin)], limit=1)
                if sale_order:
                    # Find the corresponding sale order line
                    sale_line = sale_order.order_line.filtered(lambda l: l.product_id == production.product_id and l.product_uom_qty == production.product_qty)
                    if sale_line:
                        # Assign the product tag from the sale order line to the manufacturing order
                        production.mo_product_tag_ids = sale_line.sale_product_tag_ids.ids
