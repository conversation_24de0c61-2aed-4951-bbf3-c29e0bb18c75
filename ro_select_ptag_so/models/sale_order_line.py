from odoo import models, fields, api

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    sale_product_tag_ids = fields.Many2many('product.tag', string='Product Tag', domain="[('id', 'in', available_tag_ids)]")

    available_tag_ids = fields.Many2many('product.tag', compute='_compute_available_tags' )

    @api.depends('product_id')
    def _compute_available_tags(self):
        for record in self:
            record.available_tag_ids = record.product_id.product_tmpl_id.product_tag_ids
    
    def _action_launch_stock_rule(self, previous_product_uom_qty=False):
        super(SaleOrderLine, self)._action_launch_stock_rule(previous_product_uom_qty)

        for line in self:
            self._product_tag_to_mo(line)

            self._product_tag_to_delivery(line)

    def _product_tag_to_mo(self, line):
        if line.product_id.bom_ids:
            domain = [
                ('origin', '=', line.order_id.name),
                ('product_id', '=', line.product_id.id),
                ('state', 'in', ['confirmed', 'planned', 'progress'])]
            manufacturing_orders = self.env['mrp.production'].search(domain)
            for mo in manufacturing_orders:
                mo.mo_product_tag_ids = line.sale_product_tag_ids.ids
                
    def _product_tag_to_delivery(self, line):
        stock_moves = self.env['stock.move'].search([
        ('sale_line_id', '=', line.id),
        ('state', '!=', 'cancel')])
        for move in stock_moves:
            move.stock_product_tag_ids = line.sale_product_tag_ids.ids