<odoo>
  <data>
           <record id="hr_employee_form_custom" model="ir.ui.view" >
            <field name="name">hr.employee.form.inherited</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
               
              <xpath expr="//field[@name='work_email']" position="after">
                <field name="department_id" position="move"/>
              </xpath> 
              <!-- <xpath expr="//field[@name='work_email']" position="before">
                <field name="company_id" position="move"/>
              </xpath>  -->
               <xpath expr="//field[@name='department_id']" position="after">
                <field name="parent_id" position="move"/>
              </xpath> 
            
              <xpath expr="//field[@name='parent_id']" position="attributes">
              
                <attribute name="string">Manager director</attribute>
              </xpath> 

              <xpath expr="//field[@name='parent_id']" position="after">
                          <field name="ro_start_date" string="Start Date"/>
                           <field name="ro_active_or_not" string="Active / Not Active"/>            
                                    
                                    <!-- <field name="ro_time_off_approval" string="time off approval"/> -->
                                     <!-- <field name="ro_sr" string="SR"/> -->
   
              </xpath>

              <xpath expr="//field[@name='ro_active_or_not']" position="after">
                <field name="private_street" position="move"/>
              </xpath>
              <xpath expr="//field[@name='private_street']" position="attributes">
              
                <attribute name="string">Address</attribute>
              </xpath> 
               <xpath expr="//field[@name='private_street']" position="after">
                <field name="identification_id" position="move"/>
              </xpath>
              <xpath expr="//field[@name='identification_id']" position="attributes">
              
                <attribute name="string">National ID</attribute>
              </xpath> 
              <xpath expr="//field[@name='identification_id']" position="after">
                <field name="birthday" position="move"/>
              </xpath>

               <xpath expr="//field[@name='birthday']" position="after">  
                     <field name="ro_gender" string="Gender"/>            
                    <field name="ro_graduated" string="Graduated"/>
                    <field name="ro_social_insurance" string="Social Insurance Y/Not Yet"/>
                    <field name="ro_insurance_no" string="Social Insurance No"/>
                    <field name="ro_social_insurance" string="Social Insurance"/>


              </xpath>
               <xpath expr="//field[@name='job_id']" position="after">
                <field name="bank_account_id" position="move"/>
                <field name="ro_employee_id_bank" string="Employee ID Bank"/>

                
              </xpath>
               <xpath expr="//field[@name='ro_employee_id_bank']" position="after">
                <field name="private_phone" position="move"/>
              </xpath>
              <xpath expr="//field[@name='private_phone']" position="attributes">
              
                <attribute name="string">Mobile</attribute>
              </xpath> 
              <xpath expr="//field[@name='private_phone']" position="after">
                <field name="mobile_phone" position="move"/>
              </xpath>
              <xpath expr="//field[@name='mobile_phone']" position="after">
                <field name="private_email" position="move"/>
              </xpath>
               <xpath expr="//field[@name='private_email']" position="after">
                <field name="work_email" position="move"/>
              </xpath>
               <xpath expr="//field[@name='work_email']" position="attributes">
              
                <attribute name="string">Email Xeed</attribute>
              </xpath> 
              <xpath expr="//field[@name='work_email']" position="after">
                     <field name="ro_finger_Print_id" string="Finger Print ID"/>
              </xpath>
               <xpath expr="//field[@name='work_phone']" position="attributes">
                      <attribute name="invisible">1</attribute>
              </xpath>
              
              
                                   
             

             

              <!-- <xpath expr="//field[@name='job_id']" position="after"> -->
                              
                                    
                                    <!-- <field name="ro_email_private" string="Email private"/> -->
                                                              
                <!-- </xpath> -->
              
            </field>
        </record>
  </data>
</odoo>
