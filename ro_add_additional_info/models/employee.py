from odoo import models, fields

class HrEmployeeCustom (models.Model):
    _inherit = 'hr.employee'

    ro_finger_Print_id = fields.Char(string='Finger Print ID')

    ro_gender = fields.Char(string='Gender')
    ro_graduated = fields.Char(string='Graduated')
    



    # ro_employee_id = fields.Char(string='Employee ID')

    # ro_time_off_approval = fields.Char(string='Time off approval')

    ro_insurance_no = fields.Char(string='Insurance No')

    ro_active_or_not = fields.Char(string='Active / Not Active')

    ro_start_date = fields.Date(string='Start Date')

    ro_social_insurance = fields.Char(string='Social Insurance Y/Not Yet')

    ro_social_insurance = fields.Char(string='Social Insurance')

    # ro_email_private = fields.Char(string='Email private')
    ro_employee_id_bank = fields.Char(string='Employee ID Bank')

    








    







      

