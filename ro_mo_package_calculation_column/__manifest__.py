# -*- coding: utf-8 -*-
{
    'name': "MO Package Column Calculation",

    'summary': "add column in mo screen",

    'description': """
            Add a column to the Manufacturing Order (MO) screen. This column is called 'Package'
            and depends on the 'package calculation' checkbox that exists in the product.
            Any value entered in this column should be multiplied by the net weight that exists in the product,
            and the result should be displayed in to consumed field 
    """,

    'author': "Roayatec",
    'website': "https://www.roayatec.com",
    'category': 'Manufacturing',
    'version': '17.0',

    # any module necessary for this one to work correctly
    'depends': ['base','product','mrp','stock'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/mrp_production.xml',
        'views/product_template..xml',
    ],
    'license': 'OPL-1',
   
}

