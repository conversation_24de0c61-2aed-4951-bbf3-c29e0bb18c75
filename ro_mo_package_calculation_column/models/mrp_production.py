from odoo import models, fields, api , _, Command
from collections import defaultdict
from odoo.tools import float_compare, float_round, float_is_zero, format_datetime

from odoo.addons.mrp.models.mrp_production import MrpProduction

@api.depends('company_id', 'bom_id', 'product_id', 'product_qty', 'product_uom_id', 'location_src_id')
def _compute_move_raw_ids(self):
        for production in self:
            # print("thissssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss")
            if production.state != 'draft' or self.env.context.get('skip_compute_move_raw_ids'):
                continue
            list_move_raw = [Command.link(move.id) for move in production.move_raw_ids.filtered(lambda m: not m.bom_line_id)]
            if not production.bom_id and not production._origin.product_id:
                production.move_raw_ids = list_move_raw
                # print("firsttttttttttttttttttttttttttttttttttttttttttttt")
                # print(production.move_raw_ids)
            if any(move.bom_line_id.bom_id != production.bom_id or move.bom_line_id._skip_bom_line(production.product_id)\
                for move in production.move_raw_ids if move.bom_line_id):
                production.move_raw_ids = [Command.clear()]
                # print("secondddddddddddddddddddddddddddddddddddddddddddddddddddddddddd")
                # print(production.move_raw_ids)
                
            if production.bom_id and production.product_id and production.product_qty > 0:
                # keep manual entries
                moves_raw_values = production._get_moves_raw_values()
                move_raw_dict = {move.bom_line_id.id: move for move in production.move_raw_ids.filtered(lambda m: m.bom_line_id)}
                # print("thirddddddddddddddddddddddddddddddddddddddddddddd")
                # print(move_raw_dict)
                # print(moves_raw_values)
                
                for move_raw_values in moves_raw_values:
                    if move_raw_values['bom_line_id'] in move_raw_dict:
                        # update existing entries
                        list_move_raw += [Command.update(move_raw_dict[move_raw_values['bom_line_id']].id, move_raw_values)]
                        # print("fifthhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh")
                        # print(move_raw_dict)
                        
                    else:
                        # add new entries
                        list_move_raw += [Command.create(move_raw_values)]
                        # print("sixsssssssssssssssssssssssssssssssssssssssssssssssssssssssssss")
                production.move_raw_ids = list_move_raw
                
                    
                # print("sevenssssssssssssssssssssssssssssss")
                # print(production.move_raw_ids)
            else:
                production.move_raw_ids = [Command.delete(move.id) for move in production.move_raw_ids.filtered(lambda m: m.bom_line_id)]
                # print("eighttttttttttttttttttttttttttttttttt")
                print(production.move_raw_ids)
            
            for move_raw_values in production.move_raw_ids:
                if production.product_id.weight >0.0 and move_raw_values.ro_package_value > 0.0:
                    move_raw_values.product_uom_qty = production.product_id.weight * move_raw_values.ro_package_value
            #    old
                # if move_raw_values.product_id.ro_gross_weight >0.0 and move_raw_values.ro_package_value > 0.0:
                #     move_raw_values.product_uom_qty = move_raw_values.product_id.ro_gross_weight * move_raw_values.ro_package_value    
            

MrpProduction._compute_move_raw_ids = _compute_move_raw_ids    



# def _get_consumption_issues(self):
#         """Compare the quantity consumed of the components, the expected quantity
#         on the BoM and the consumption parameter on the order.

#         :return: list of tuples (order_id, product_id, consumed_qty, expected_qty) where the
#             consumption isn't honored. order_id and product_id are recordset of mrp.production
#             and product.product respectively
#         :rtype: list
#         """
#         issues = []
#         if self.env.context.get('skip_consumption', False):
#             return issues
#         for order in self:
#             if order.consumption == 'flexible' or not order.bom_id or not order.bom_id.bom_line_ids:
#                 continue
#             expected_move_values = order._get_moves_raw_values()
#             expected_qty_by_product = defaultdict(float)
#             for move_values in expected_move_values:
#                 move_product = self.env['product.product'].browse(move_values['product_id'])
#                 move_uom = self.env['uom.uom'].browse(move_values['product_uom'])
#                 print(move_values)
#                 # if move_values.get('product_id.ro_gross_weight') and move_values.get('ro_package_value'):
#                 #    move_product_qty = move_values['product_id'].ro_gross_weight * move_values['ro_package_value']
                   
#                 #    print("nooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooooo")
#                 #    print(move_product_qty)
#                 # else:
#                 move_product_qty = move_uom._compute_quantity(move_values['product_uom_qty'], move_product.uom_id)
                        
#                 print("llllllllllllllllllllllllllllllllllllllllllllllllllll")
#                 print(move_product_qty)
                
#                 expected_qty_by_product[move_product] += move_product_qty * order.qty_producing / order.product_qty               
            
#             done_qty_by_product = defaultdict(float)
#             for move in order.move_raw_ids:
#                 quantity = move.product_uom._compute_quantity(move._get_picked_quantity(), move.product_id.uom_id)
#                 rounding = move.product_id.uom_id.rounding
#                 # if move.product_id.ro_gross_weight and move.ro_package_value > 0.0:
#                 #     move.product_uom_qty = move.product_id.ro_gross_weight * move.ro_package_value
#                 #     print("gey")
#                 # extra lines with non-zero qty picked
#                 if move.product_id not in expected_qty_by_product and move.picked and not float_is_zero(quantity, precision_rounding=rounding):
#                     issues.append((order, move.product_id, quantity, 0.0))
#                     continue
#                 done_qty_by_product[move.product_id] += quantity if move.picked else 0.0

#             # origin lines from bom with different qty
#             for product, qty_to_consume in expected_qty_by_product.items():
#                 quantity = done_qty_by_product.get(product, 0.0)
#                 if float_compare(qty_to_consume, quantity, precision_rounding=product.uom_id.rounding) != 0:
#                     issues.append((order, product, quantity, qty_to_consume))
#                 # for move_raw_values in production.move_raw_ids:
#                 # if move_raw_values.product_id.ro_gross_weight and move_raw_values.ro_package_value > 0.0:
#                 #     move_raw_values.product_uom_qty = move_raw_values.product_id.ro_gross_weight * move_raw_values.ro_package_value            
                    
           
            
#         return issues
# MrpProduction._get_consumption_issues = _get_consumption_issues    


 
