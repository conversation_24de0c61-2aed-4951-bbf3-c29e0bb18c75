from odoo import models, fields,api,_

class StockMove(models.Model):
    _inherit = 'stock.move'

    ro_package_value = fields.Float('Package')
    ro_calculate_package = fields.Boolean(
        'Calculate Package',
        default=False,
        related='product_id.ro_calculate_package'
    )
    
    @api.onchange('ro_package_value', 'product_id',)
    def _onchange_package_or_product(self):
        """
        Recalculates `product_uom_qty` based on `ro_package_value` 
        and the product's gross weight if `ro_calculate_package` is True.
        """
        for move in self:
            if move.ro_calculate_package and move.ro_package_value >0.0:
                # product_weight = move.product_id.ro_gross_weight
                product_weight = move.raw_material_production_id.product_id.weight
                
                if product_weight > 0:
                    move.product_uom_qty = move.ro_package_value * product_weight
                    # move.bom_line_id.product_qty = move.ro_package_value * product_weight
                    
                else:
                    move.product_uom_qty = 0.0
       
# from odoo.addons.mrp.models.stock_move import StockMove
# from odoo.tools import float_compare, float_round, float_is_zero, OrderedSet


# # @api.depends('raw_material_production_id.qty_producing', 'product_uom_qty', 'product_uom')
# # def _compute_should_consume_qty(self):
# #             for move in self:
# #                 mo = move.raw_material_production_id
# #                 product_weight = move.product_id.ro_gross_weight
# #                 if move.ro_calculate_package and move.ro_package_value:
# #                      move.should_consume_qty = move.ro_package_value * product_weight
# #                 else:     
# #                     if not mo or not move.product_uom:
# #                         move.should_consume_qty = 0
# #                         continue
# #                     move.should_consume_qty = float_round((mo.qty_producing - mo.qty_produced) * move.unit_factor, precision_rounding=move.product_uom.rounding)
# #                 print("mrppppppppppppppppppppppppppppppppppppppppppppppppppppppppp")
# #                 print(move.should_consume_qty)
             
              
                
# # StockMove._compute_should_consume_qty = _compute_should_consume_qty               
                
                







       

    
    
    
    
    
    
    
    





    







      

