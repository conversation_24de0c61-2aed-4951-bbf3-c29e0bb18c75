<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.ui.view" id="check_action_inbank_form_view">
        <field name="name">account.check.action.inbank.wizard.form</field>
        <field name="model">account_check_action</field>
        <field name="arch" type="xml">
            <form string="Check Action">
                <group>
                    <field name="action_type" invisible="0" readonly="1"/>
                    <group>
                        <field name="journal_id" invisible="action_type != 'bank_debit'" 
                            required="action_type == 'bank_debit'" domain="[('type', '=', 'bank')]"/>
                        <field name="account_id" required="action_type in ('inbank','returned')" 
                            invisible="action_type == 'bank_debit'"/>
                        <field name="debited_account_id" invisible="action_type != 'bank_debit'"
                            readonly="action_type == 'bank_debit'"/>
                        <field name="partner_id" invisible="action_type != 'returned'"/>
                        <field name="check_ids" widget="many2many_tags" invisible="1"/>
                        <field name="portfolio" invisible="1"/>
                    </group>
                    <group>
                        <field name="company_id" invisible="1"/>
                        <field name="date"/>
                        <field name="inbank_account_id"
                               readonly="1"
                               invisible="action_type == 'inbank'"/>
                        <field name="sequence" force_save="1" invisible="1"/>
                    </group>
                </group>
                <footer>
                    <button string="Confirm" name="action_confirm" type="object" class="oe_highlight"
                        invisible="portfolio == True"/>
                    <button string="Confirm" name="action_confirm_portfolio" type="object" class="oe_highlight"
                        invisible="portfolio == False"/>
                    or
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>


    <record model="ir.ui.view" id="check_action_inbank_select_view">
        <field name="name">account.check.action.inbank.wizard.form</field>
        <field name="model">account_check_action</field>
        <field name="arch" type="xml">
            <form string="Check Action">
                <group>
                    <field name="action_type" invisible="0" readonly="1"/>
                    <group>
                        <field name="company_id" invisible="1"/>
                        <field name="date"/>
                        <field name="inbank_account_id"
                               readonly="1"
                               invisible="action_type == 'inbank'"/>
                        <field name="sequence" force_save="1" invisible="1"/>
                    </group>
                </group>
                <footer>
                    <button string="Confirm" name="action_confirm" type="object" class="oe_highlight"/>
                    or
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="action_wizard_inbank" model="ir.actions.act_window">
        <field name="name">Check In Bank</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account_check_action</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>
