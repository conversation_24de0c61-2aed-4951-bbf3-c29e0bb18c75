<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_account_check_journal_form" model="ir.ui.view">
        <field name="name">account_check.account.journal.form</field>
        <field name="model">account.journal</field>
        <field name="inherit_id" ref="account.view_account_journal_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='advanced_settings']/group" position="inside">
                <group string="Check Configuration"> 
                    <field name="payment_received_bool" />
                    <field name="payment_issued_bool" />
                </group>
            </xpath>
            <xpath expr="//notebook/page/group/group[2]" position="after">
                <group string="Check Accounts">
                    <field name="holding_check_account_id" invisible="payment_received_bool == False" />
                    <field name="under_collection_check_account_id" invisible="payment_received_bool == False"/>
                    <field name="safty_check_account_id" invisible="payment_received_bool == False"/>
                    <field name="deferred_check_account_id" invisible="payment_issued_bool == False" />
                </group>
            </xpath>

            <xpath expr="//field[@name='inbound_payment_method_line_ids']//tree//field[@name='payment_account_id']" position="after">
                <field name="safety_check" />
            </xpath>

        </field>
    </record>
</odoo>
