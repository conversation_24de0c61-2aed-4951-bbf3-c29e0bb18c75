<odoo>
        <record id="account_journal_dashboard_kanban_view_inherited" model="ir.ui.view">
            <field name="name">account.journal.dashboard.kanban.inherited</field>
            <field name="model">account.journal</field>
            <field name="inherit_id" ref="account.account_journal_dashboard_kanban_view" />
            <field name="arch" type="xml">
                <xpath expr="//t[@t-name='JournalBodyBankCash']//div[contains(@class, 'o_kanban_primary_right')]" position="inside">
                    <div t-if="dashboard.show_third_checks">
                        <div class="row">
                            <div class="col-xs-6">
                                <a type="object" name="open_action_checks" context="{'check_type': 'third_check'}">
<!--                                 <a type="action" name="%(action_collected_check)d">-->
                                    <t t-esc="dashboard.num_holding_third_checks"/>
                                    <t>Collected Check(s) on hand</t>
                                </a>
                            </div>
                            <div class="col-xs-6 text-right">
                                <span><t t-esc="dashboard.holding_amount"/></span>
                            </div>
                        </div>
                        <!-- not so usefull -->
<!--                         <div class="row">
                            <div class="col-xs-12">
                                <a type="object" name="open_transfer_money">Checks Deposits</a>
                            </div>
                        </div> -->
                    </div>
                    <div t-if="dashboard.show_issue_checks">
                        <div class="row">
                            <div class="col-xs-6">
                                <a type="object" name="open_action_checks" context="{'check_type': 'issue_check'}">
<!--                                 <a type="action" name="%(action_issued_check)d">-->
                                    <t t-esc="dashboard.num_handed_issue_checks"/>
                                    <t>Issued Check(s) handed</t>
                                </a>
                            </div>
                            <div class="col-xs-6 text-right">
                                <span><t t-esc="dashboard.handed_amount"/></span>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
</odoo>
