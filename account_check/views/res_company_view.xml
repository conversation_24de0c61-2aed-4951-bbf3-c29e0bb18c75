<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="view_company_form" model="ir.ui.view">
            <field name="name">res.company.form</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page">
                    <group>
                        <field name="rejected_check_account_id" domain="[('company_id', '=', id)]"/>
                        <field name="deferred_check_account_id" domain="[('company_id', '=', id)]"/>
                        <field name="holding_check_account_id" domain="[('company_id', '=', id)]"/>
                    </group>
                </xpath>
            </field>
        </record>
</odoo>
