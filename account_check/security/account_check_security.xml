<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
		<record id="account_check_rule" model="ir.rule">
            <field name="name">Check Multi-Company</field>
            <field name="model_id" ref="model_account_check"/>
            <field eval="True" name="global"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
        </record>
    </data>
</odoo>
