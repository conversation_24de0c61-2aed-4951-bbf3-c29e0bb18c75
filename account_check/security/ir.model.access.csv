id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
account_check_access_full,account_check_access_full,model_account_check,account.group_account_invoice,1,1,1,1
account_check_access_global,account_check_access_global,model_account_check,,1,1,0,0
account_check_operation_access_global,account_check_operation_access_global,model_account_check_operation,,1,0,0,0
account_check_operation_access_full,account_check_operation_access_full,model_account_check_operation,account.group_account_invoice,1,1,1,1
base.access_ir_sequence_group_user,base.ir_sequence group_user,base.model_ir_sequence,base.group_user,1,1,0,0
manager_access_check_portfolio,check.portfolio.manager,model_check_portfolio,,1,1,1,1
manager_access_check_portfolio_lines,check.portfolio.lines.manager,model_check_portfolio_lines,,1,1,1,1
access_account_check_action_wizard_group_user,account.check.action.wizard,model_account_check_action_wizard,base.group_user,1,1,1,1
access_account_sell_check_wizard_group_user,account.sell.check.wizard,model_account_sell_check_wizard,base.group_user,1,1,1,1
access_action_handed_wizard_group_user,action.handed.wizard,model_action_handed_wizard,base.group_user,1,1,1,1
access_account_check_action_group_user,account_check_action,model_account_check_action,base.group_user,1,1,1,1
access_check_return_treasury_group_user,check.return.treasury,model_check_return_treasury,base.group_user,1,1,1,1
access_action_transfer_treasury_wizard_group_user,action.transfer.treasury.wizard,model_action_transfer_treasury_wizard,base.group_user,1,1,1,1
access_account_to_partner_wizard_group_user,account.to.partner.wizard,model_account_to_partner_wizard,base.group_user,1,1,1,1




