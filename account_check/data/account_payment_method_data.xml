<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="account_payment_method_receive_safty_check" model="account.payment.method">
            <field name="name">Receive Safty Checks</field>
            <field name="code">received_safty_check</field>
            <field name="payment_type">inbound</field>
        </record> 

        <record id="account_payment_method_received_third_check" model="account.payment.method">
            <field name="name">Receive Customer Checks</field>
            <field name="code">received_third_check</field>
            <field name="payment_type">inbound</field>
        </record> 

        <record id="account_payment_method_delivered_third_check" model="account.payment.method">
            <field name="name">Delivered Third Check</field>
            <field name="code">delivered_third_check</field>
            <field name="payment_type">outbound</field>
        </record>

        <record id="account_payment_method_issue_check" model="account.payment.method">
            <field name="name">Issue Check</field>
            <field name="code">issue_check</field>
            <field name="payment_type">outbound</field>
        </record>

    </data>
</odoo>
