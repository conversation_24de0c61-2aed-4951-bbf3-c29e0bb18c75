from odoo import models, fields, api, _


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    
    # ro_actual_qty = fields.Float(string="Quantity")
    ro_note=fields.Many2one(string='Note', comodel_name='sale.note')
    # ro_compare_qty=fields.Boolean(default=False,
    #     compute='_compare_quantities' )
    
    
    # @api.depends('product_uom_qty','ro_actual_qty')
    # def _compare_quantities(self):
    #     for line in self:
    #         # if line.product_uom_qty > line.ro_actual_qty:
    #         if line.product_uom_qty < line.ro_actual_qty:
    #             line.ro_compare_qty =True
    #         else:
    #             line.ro_compare_qty=False
                
              
              
    @api.model_create_multi
    def create(self, vals_list):
        result = super().create(vals_list)
        for line in result:
                msg_parts = []
                """if line.ro_actual_qty:
                    msg_parts.append(" Actual Quantity: %s" % line.ro_actual_qty)"""
                if line.product_uom_qty:
                    msg_parts.append(" Quantity: %s" % line.product_uom_qty)
                if line.ro_note:
                    msg_parts.append(" Note: %s" % line.ro_note.name)
               
                if msg_parts:
                    msg_body = '%s added with' % line.product_id.name +', '.join(msg_parts)
                
                # if msg_body:
                    line.order_id.message_post(body=msg_body)
        
        return result         
                
    
    
    def write(self, vals):
        for line in self:
            msg_parts = []
            """if 'ro_actual_qty' in vals:
                msg_parts.append(_(" Actual Quantity: %s → %s") % (line.ro_actual_qty,vals['ro_actual_qty']))"""
            if 'product_uom_qty' in vals:
                msg_parts.append(_(" Quantity: %s → %s") % (line.product_uom_qty,vals['product_uom_qty']))
            if 'ro_note' in vals:
                msg_parts.append(_(" Note: %s → %s" )% (line.ro_note.name, self.env['sale.note'].browse(vals['ro_note']).name))
                
            msg_body = '%s modified with' % line.product_id.name +', '.join(msg_parts)
            
            if msg_body:
                line.order_id.message_post(body=msg_body)

        return super().write(vals)
        
       
        
    def unlink(self):
        for line in self:
            line.order_id.message_post(body='%s has been Deleted!' % line.product_id.name)
        return super().unlink()