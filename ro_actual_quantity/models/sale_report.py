from odoo import models, fields, api, _


class SaleReport(models.Model):
    _inherit = 'sale.report'
    
    #ro_actual_qty = fields.Float(string="Quantity")
    ro_note=fields.Many2one(string='Note', comodel_name='sale.note')
    
    
    
    def _select_additional_fields(self):
        res = super()._select_additional_fields()
        #res['ro_actual_qty']= 'l.ro_actual_qty'
        res['ro_note']= 'l.ro_note'
        return res
    
    
    def _group_by_sale(self):
        res = super()._group_by_sale()
        #res += """, ro_actual_qty, ro_note"""
        res += """, ro_note"""
        return res
    
