from odoo import models, fields, api, _
from odoo.exceptions import UserError



class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    def action_confirm(self):
        for order_line in self.order_line:
            if not order_line.ro_note: #and order_line.ro_actual_qty < order_line.product_uom_qty:
                raise UserError(_('Please add note for %s before Confirmation', order_line.product_id.name))
        return super().action_confirm()
    
