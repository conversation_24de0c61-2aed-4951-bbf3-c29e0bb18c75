from odoo import models, fields

class HrEmployeeInherited (models.Model):
    _inherit = 'hr.employee'
      



    ro_copy_national_id = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Copy of National Id",
      
    )


    ro_birth_certificate = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Birth certificate",
         
    )

    ro_graduation_certificate = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Graduation certificate",
        
    )

    ro_military_certificate = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Military Certificate",
     
    )

    ro_personal_photo = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Personal Photo",
      
    )

    ro_criminal_record = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Criminal Record",
        
    )

    ro_form = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Form 111",
       
    )

    ro_social_insturance_print = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Social Insturance Print",
       
    )

    ro_labour_office_paper = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Labour Office Paper",
      
    )

    ro_offer_letter = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Offer Letter",
       
    )
    ro_contract = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Contract",
      
    )
    ro_confidentiality = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Confidentiality Agreement",
       
    )
    ro_social_insturance_axa = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Social Insturance Axa",
       
    )
    ro_interview_form = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Interview Evaluatio Form",
      
    )
    ro_renewal_contract = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Renewal Contrac",
      
    )
    ro_labor_office_receiving = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Labor office receiving",
        
    )
    ro_date_contract = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Date of Contract",
      
    )
    ro_receiving_items = fields.Selection(
        [('yes', 'Yes'), ('no', 'No')],
        string ="Receiving Items",
         
    )