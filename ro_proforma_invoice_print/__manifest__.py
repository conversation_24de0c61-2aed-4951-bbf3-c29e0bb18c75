# -*- coding: utf-8 -*-
{
    'name': "Pro Forma invoice print",

    'summary': "invoice print",

    'description': """
       invoice print
    """,

    'author': "Roaya",
    'website': "https://www.roayadm.com",
    'category': 'account',
    'version': '17.0',

    # any module necessary for this one to work correctly
    'depends': ['base','account','ro_product_attributes_fields','ro_logistics','product','ro_customer_country','sale','ro_commerical_invoice_print','ro_final_commerical_invoice_print'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        
        # 'views/account_move_view.xml',
        # 'views/sale_view.xml',
        'views/templates.xml',
    ],
    'installable': True,
    'license': 'OPL-1',
    
}

