<?xml version="1.0" encoding="utf-8"?>
<odoo>
 <record id="paperformat_commrtical" model="report.paperformat">
            <field name="name">Commerical invoice paperformat</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">35</field>
            <field name="margin_bottom">20</field>
            <field name="margin_left">10</field>
            <field name="margin_right">10</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">30</field>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">108</field>
        </record>

  

        <template id="invoice_external_layout">
        <div t-attf-class="header" t-att-style="report_header_style">

              <style>
                                    .no-border-tbody tbody{border:0px !important;}

                                    .no-border{border:0px !important;}
                                    .no-border tbody td{ 
                                        border-top:0px !important;
                                        border-bottom:0px !important;                                        }
                                    .no-border tbody tr{border:0px !important;}
                                    <!-- .add-border tbody tr{border:1px !important;}
                                    .add-border tbody td{border:1px !important;} -->

                                    .td {
                                            border-top:border:0px !important;
                                            border-bottom:border:0px !important;
                                            border-left: 1px solid black !important;
                                            border-right: 1px solid black !important;
                                          
                                        }

                            </style>
            <div class="row no-border" style="padding-bottom:10px;">
           
               
               
                 <table class="no-border-tbody" style="border:none;margin-top:10px;font-family:'PT Serif',serif;">
                    <td class="no-border col-5" style="border:none;" >
                     <img t-att-src="'/ro_commerical_invoice_print/static/src/img/logoh.png'" alt="Company Logo" style="float: left; margin-right: 10px;"/>
               
                   </td>
                   <td class="no-border col-7" style="text-align:left;border:none;" >

                          
                    <b class="no-border" style="font-size:16; padding-bottom:10px;font-family:'PT Serif',serif;"> Commercial Invoice</b>
                  
                
                 </td>
                 </table>
               
            </div>
            
        </div>
      
        <!-- <div t-attf-class="header" t-att-style="report_header_style">
           

                                   
            <table class="no-border-tbody" style="border:none;margin-top:10px;font-family:'PT Serif',serif;">
                <td class="no-border col-8" style="border:none;" >
                 <img t-att-src="'/ro_sec_commerical_invoice_print/static/src/img/logoh.png'" alt="Company Logo" style="float: left; margin-right: 10px;"/>
           
               </td>
               <td class="no-border col-4" style="text-align:center;border:none;" >

                      
                <b class="no-border" style="font-size:16; padding-bottom:10px;font-family:'PT Serif',serif;"> Commerical Invoice</b>
              
            
             </td>
             </table>

        </div> -->
 
        <!-- <div t-attf-class="article o_report_layout_standard o_company_#{o.company_id.id}_layout {{  'o_report_layout_background' if o.company_id.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % o.company_id.layout_background_image.decode('utf-8') if o.company_id.layout_background_image and o.company_id.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if o.company_id.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')"> -->
        <!-- <div t-attf-class="article o_report_layout_standard o_company_#{o.company_id.id}_layout {{ 'o_report_layout_background' if o.company_id.layout_background in ['Geometric', 'Custom'] else '' }}" t-attf-style="background-image: url('/ro_commerical_invoice_print/static/src/img/water_mark.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">   
         -->
         <div t-attf-class="article o_report_layout_standard " t-attf-style="background-image: url('/ro_commerical_invoice_print/static/src/img/water_mark.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">   
         
            <div class="pt-5">
           
                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
        </div>

          <div t-attf-class="footer">
            <div style="margin-top:20px;">
                <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                 </div>  
            <div class="row">
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>

                                    <!-- <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;"> -->
                                    <!-- </a> -->      
                                        <span style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;"><EMAIL> </span>
                                   
                                    
                                    </div>
                                   
                                    
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/world-wide-web.png'" alt="web" style="height:25px;wedth:25px;"/>

                                      <!-- <a t-att-href="'http://www.xeedcorp.com/'" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">
                                                            www.xeedcorp.com
                                     </a> -->
                                     <span style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">www.xeedcorp.com</span>
                                    </div>
                                   
                                  
                                     <div class="col-4" style="text-align:center;font-family:'PT Serif',serif;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/telephone.png'" alt="phone" style="height:25px;wedth:25px;"/>
                                     
                                     <strong><span style="color:#5a855f;">+2010 915 56 556</span></strong>
                                    </div>
                             </div>
            </div>

       
    </template>

       <record id="action_commerical_invoice_report" model="ir.actions.report">
            <field name="name">Commerical Invoice</field>
            <field name="model">account.move</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_commerical_invoice_print.commerical_report_template</field>
            <field name="report_file">ro_commerical_invoice_print.commerical_report_template</field>
             <field name="paperformat_id" ref="ro_commerical_invoice_print.paperformat_commrtical"/>
            <field name="print_report_name">'Commerical Invoice'</field>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_type">report</field>
        </record>


         <template id="commerical_report_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="ro_commerical_invoice_print.invoice_external_layout">
                        <link rel="preconnect" href="https://fonts.googleapis.com"/>
                        <link rel="preconnect" href="https://fonts.gstatic.com"/>
                        <link href="https://fonts.googleapis.com/css2?family=PT+Serif:ital,wght@0,400;0,700;1,400;1,700" rel="stylesheet"/>
                        <t t-if="o.line_ids[0].sale_line_ids">

                        <div class="page">
                          <div class="row no-outline" style="width: 100%;display: flex; justify-content: space-between; border:none;">
                                  
                                    <style>
                                    .no-border-tbody tbody{border:0px !important;}

                                    .no-border{border:0px !important;}
                                    .no-border tbody td{ 
                                        border-top:0px !important;
                                        border-bottom:0px !important;                                        }
                                    .no-border tbody tr{border:0px !important;}
                                  
                                    .td {
                                            border-top:border:0px !important;
                                            border-bottom:border:0px !important;
                                            border-left: 1px solid black !important;
                                            border-right: 1px solid black !important;
                                          
                                        }

                            </style>
                                   
                                   
                                    <div class="col-7" style="text-align: left; font-size:12px; ">
                                    <strong><div style="font-family:'PT Serif',serif;">XEED FOR IMPORT AND EXPORT</div></strong>
                                    <div style="font-family:'PT Serif',serif;" >Building No. 37, Walk of Cairo Trade Center,</div>
                                    <div style="font-family:'PT Serif',serif;" >Kilo 38, Cairo-Alexandria Desert Road -Sheikh Zayed,Egypt</div>
                                    </div>
                                    <div class="col-5 no-border-tbody" style="text-align: right;margin-right:40px;font-size:12px;border: none;">
                                    
                                    <table class="no-border-tbody" style="width: 100%;  direction: rtl;border: 0px !important;margin-right:5px;padding-bottom:60px;font-family:'PT Serif',serif;">
                                   <!-- add title in right of page  -->
                                        <!-- <tr style="border: none;">
                                    
                                        <p style="text-align:left;font-size:16px;border: none; padding-right:30px;font-family:'PT Serif',serif;"> <b >Commerical Invoice</b> </p>
                                        
                                      
                                        </tr> -->

                                        <tr style="border: none;direction:ltr">
                                            
                                            
                                            <td style="border: none;width:200px;text-align:left;font-size:12px;">
                                                
                                                    <b><span t-esc="o.name"></span></b> 
                                                
                                            </td>
                                            <td style="border: none;width:150px;text-align:left;font-size:12px;">
                                                    
                                                    <b >Invoice no : </b>
                                                
                                            </td>
                                            
                                        </tr>
                                        <tr style="border: none;direction:ltr">
                                            
                                            <td style="border: none;width:200px;text-align:left;font-size:12px;">
                                                
                                                    <!-- <b><span t-field="o.invoice_date" t-options="{'format': '%d-%b-%Y'}"></span></b>  -->
                                                    <!-- <span t-field=[o.invoice_date]></span> -->
                                                    <b><span t-esc="o.invoice_date.strftime('%d-%b-%Y')if o.invoice_date else ''"></span></b>
                                                
                                            </td>
                                            <td style="border: none;width:150px;text-align:left;font-size:12px;">
                                                
                                                    <b >Invoice Date : </b>
                                                
                                            </td>
                                            
                                        </tr>

                                        <tr style="border: none;direction:ltr">
                                            
                                            <td style="border: none;width:200px;text-align:left;font-size:12px;">
                                                
                                                    <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.bl_number"></span></b> 
                                                
                                            </td>
                                            <td style="border: none;width:150px;text-align:left;font-size:12px;">
                                                
                                                    <b >BL Number :</b>
                                                
                                            </td>
                                            
                                        </tr>

                                          <tr style="border: none;direction:ltr">
                                            
                                            <td style="border: none;width:200px;text-align:left;font-size:12px;">
                                                
                                                    <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.name"></span></b> 
                                                
                                            </td>
                                            <td style="border: none;width:150px;text-align:left;font-size:12px;">
                                                
                                                    <b >SO NO.:</b>
                                                
                                            </td>
                                            
                                        </tr>




                           
                                    
                                    </table>
                                    </div>
                            </div>
                            
                         <b><hr style="border: 1px solid #000; width:100%;font-weight: bold;"/></b>
                                <div class="row" style="width: 100%; display: flex; justify-content: space-between; margin-bottom: 10px;border: none;">
                                    <div  class="col-7" style="text-align: left;border: 0px !important;">
                                     <table class="no-border-tbody"  style="border-collapse: collapse; border: 0px !important;font-size:11px;font-family:'PT Serif',serif;font:weight:bold;">
                                     
                                     <!-- <table class="no-border table table-sm o_main_table table-bordered"> -->
                                        <tr class="no-border" style="border: none;">
                                        
                                       

                                            <tr style="border: none;">
                                        <td style="border: none;">
                                            <span style="ext-align:left;">

                                                <b><u>Bill To: </u> </b>
                                            </span>
                                             <div style="width:300px;">
                                                    <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.name"/>
                                                        <br/>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.street"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.city">
                                                           <span>,</span> 
                                                        </t>
                                                        
                                                        <!-- <br/> -->
                                            
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.city"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.zip">
                                                           <span>,</span> 
                                                        </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.zip"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.country_id.name">
                                                            <span>,</span>
                                                        </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.country_id.name"/>
                                                    </b>

                                         </div>           
                                           <!--.................. old..................................... -->
                                            <!-- <p><b> <span t-field="o.partner_id.name"></span></b></p> -->
                                            <!-- <div><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.notify_id"></span></b></div> -->
                                            <!-- <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.notify_id.street"></span></b></p> -->
                                            <!-- <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.notify_id.street2"></span></b></p> -->

                                           
                                        </td>
                                        <!--............... old old update ............... -->
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b> <span t-field="o.partner_id.name"></span></b>
                                        </td> -->

                                    </tr>
                                            
                                            
                                            </tr>
                                            <tr class="no-border"  style="border: none;">
                                                
                                                <td style="border: none;text-align:left;">
                                                    <span style="width:100px;padding-right:100px;">
                                                
                                                        <b>
                                                        Origin:
                                                        </b>
                                                    </span> 

                                                    <span> 
                                                        <b>Egypt</b></span> 
                                                </td>
                                                 <!-- <td style="border: none;text-align:left;">
                                                    <b>
                                                        <span> Egypt</span>
                                                    </b>
                                                
                                                </td> -->
                                               

                                            </tr>
                                            
                                            <tr style="border: none;">
                                                
                                                <td style="border: none;text-align:left;">
                                                <span style="width:100px;padding-right:68px;">
                                                     <b>
                                                    Vat Number:
                                                    </b>
                                                </span> 
                                                    <b> 
                                                <span t-field="o.partner_id.vat"></span>  
                                                    </b>
                                                </td>
                                                 <!-- <td style="border: none;text-align:left;">
                                                    <b>
                                                      <span t-field="o.partner_id.vat"></span>
                                                    </b>
                                                
                                                </td> -->
                                               

                                            </tr>
                                            <tr style="border: none;">
                                                
                                                <td style="border: none;text-align:left;">
                                                <span style="width:100px;padding-right:100px;" >
                                                     <b>
                                                    Agent:
                                                    </b>
                                                </span> 
                                                <b>
                                                    <span t-field="o.line_ids[0].sale_line_ids[0].order_id.agent_id"></span> 
                                                </b>  
                                                </td>
                                                 <!-- <td style="border: none;text-align:left;">
                                                    <b>
                                                      <span t-field="o.line_ids[0].sale_line_ids[0].order_id.agent_id"></span>
                                                    </b>
                                                
                                                </td> -->
                                               

                                            </tr>

                                            <tr style="border: none;">
                                                <td style="border: none;text-align:left;">
                                                    <span style="text-align:left;width:100px;padding-right:36px;">
                                                          
                                                           <b> Port of Departure :</b>
                                                    
                                                    </span>
                                                    <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.loading_port"></span> </b>
                                                </td>
                                                <!-- <td style="border: none;text-align:left;">
                                                    <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.loading_port"></span> </b>
                                                </td> -->
                                            </tr>   
                                            <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;text-align:left;width:100px;padding-right:93px;">
                                                     <b>    Vessel : </b>
                                                     
                                                     
                                                
                                                </span>
                                                <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.vessel_name"></span></b>
                                            </td>

                                            <!-- <td style="border: none;text-align:left;">
                                                    <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.vessel_name"></span></b>
                                            </td> -->
                                        </tr>
                                         <tr style="border: none;"> 
                                            <td style="border: none;">
                                                <span style="text-align:left;width:100px;padding-right:33px;">
                                                         <b>  Number of Pallets : </b> 
                                                
                                                </span>
                                                <span>
                                                    <t t-foreach="o.invoice_line_ids" t-as="line">
                                            

                                                        <b><span t-esc="len(o.picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/></b>
         
         
                                                        
                                                     
                                                     </t>
                                                </span>
                                            </td>
                                           <!-- <td style="border: none;text-align:left;font-size:13px;">
                                           
                                            <t t-foreach="o.invoice_line_ids" t-as="line">
                                            

                                               <b><span t-esc="len(o.picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/></b>


                                               
                                            
                                            </t>
                                           
                                        </td> -->
                                        </tr>
                                         <!-- <tr style="border: none;"> 
                                            <td style="border: none; direction: ltr;">
                                                <div style="text-align:left;">
                                                           
                                                
                                                     <b>  PO :</b>
                                                
                                                </div>
                                            </td>
                                            <td style="border: none;width:300px;text-align:left;">
                                            <b>
                                                   <span t-field="o.ref" style="direction:ltr;"></span>   
                                            </b>
                                            </td>
                                            </tr> -->
                                                <tr style="border: none;"> 
                                                    <td style="border: none;text-align:left;">
                                                         <span style="text-align:left;width:100px;text-align:left;padding-right:56px;">
                                                           <b> Gross Weight :</b>    
                      

                                                            </span>
                                                            <span>
                                                                <b>
                                                                    <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_gross_weight"></span> 
                                                                    <b> <span style="padding-left:5px;">KG</span> </b>  
                                                               </b>
                                                            </span>
                                                                    </td>

                                                                    <!-- <td style="border: none;text-align:left;">
                                                                    <b>
                                                                         <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_gross_weight"></span> 
                                                                         <b> <span style="padding-left:5px;">KG</span> </b>  
                                                                    </b>
                                                                    </td> -->
                                                                </tr> 


                                            
                                                <tr style="border: none;"> 
                                                    <td style="border: none;text-align:left;">
                                                         <span style="text-align:left;width:100px;padding-right:107px;">                                                           
                                                           <b>ETD:</b>
                                                            </span>
                                                            <b><span t-esc="o.line_ids[0].sale_line_ids[0].order_id.eta_date.strftime('%d-%b-%Y') if o.line_ids[0].sale_line_ids[0].order_id.eta_date else ''" ></span> </b>
                                                                    </td>

                                                            <!-- <td style="border: none;text-align:left;">
                                                                
                                                                
                                                                    <b><span t-esc="o.line_ids[0].sale_line_ids[0].order_id.eta_date.strftime('%d-%b-%Y') if o.line_ids[0].sale_line_ids[0].order_id.eta_date else ''" ></span> </b>

                                                            
                                                            </td>         -->
                                                                </tr>                                           
                                             
                                                <!-- <tr style="border: none;"> 
                                                    <td style="border: none;width:100px;text-align:left;">
                                                         <div style="text-align:left;">
                                                          <b>Loading Date:</b>
                                                                                
                                                                        
                                                                                
                                                            </div>
                                                                    </td>

                                                                    <td style="border: none;width:300px;text-align:left;">
                                                                            <b>
                                                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.loading_date" t-options='{"widget": "date"}'></span>
                                                                            </b>
                                                                    
                                                                    </td>
                                                                </tr>     -->

                                    
                                    </table>
                                    </div>
                                    <div  class="col-5" style="text-align:left; margin-right:10px;font-size:11px;border: none;">
                                    <table class="no-border-tbody" style="border-collapse: collapse; width: 100%;border: none; table-fixed;font-family:'PT Serif',serif;">
                                    <!-- <table class="no-border table table-sm o_main_table table-bordered "> -->
                                        <!-- <tr style="border: none;">
                                            <td style="border: none;">
                                                <span style=" text-align:left;">
                                                                                              
                                                   <b><u> Ship To : </u></b>
                                                
                                                </span>
                                                <div style="text-align:left;padding-bottom:20px;width:200px;text-align:left;">
                                                    <b><span t-field="o.partner_shipping_id"></span> </b>
                                                </div>
                                            </td>
                                            
                                            </tr> -->

                                             <tr style="border: none;">
                                        <td style="border: none;">
                                            <span style=" text-align:left;">

                                                <b><u>ship To: </u> </b>
                                            </span>
                                            <div style="width:300px;">
                                                    <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.name"/>
                                                        <br/>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.street"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.city">
                                                         <span>,</span>
                                                            
                                                        </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.city"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.zip">
                                                            <span>,</span>
                                                            
                                                        </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.zip"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.country_id.name">
                                                            <span>,</span>
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.country_id.name"/>
                                                    </b>

                                         </div>  
                                           
                                            <!-- <div><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id"></span></b></div>
                                            <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id.street"></span></b></p> -->
                                            <!-- <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id.street2"></span></b></p> -->


                                        </td>
                                       

                                    </tr>
                                            
                                            
                                            <tr style="border: none;">
                                            <td style="border: none;text-align:left;">
                                                <span style=" text-align:left;width:100px;padding-right:70px;">
                                                  <b> Destination :</b>
                                                
                                                    <!-- Invoice Date :<span t-field="o.invoice_date" style=" direction:ltr;"></span>  -->
                                                
                                                </span>
                                                <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.ro_country"></span>  </b>
                                            </td>
                                            <!-- <td style="border: none;text-align:left;">
                                                <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.ro_country"></span>  </b>
                                            </td> -->
                                            </tr>

                                            <tr style="border: none;">
                                                <td style="border: none;text-align:left;">
                                                    <span style="text-align:left;width:100px;padding-right:55px;">
                                                           <b>Port of Arrivial:</b>
                                                    </span>
                                                    <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.destination_port"></span></b>
                                                </td>
                                                <!-- <td style="border: none;text-align:left;">
                                                    <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.destination_port"></span></b>
                                                </td> -->
                                            </tr>   
                                            <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:45px;">

                                                   <b>Shipping Method: </b>
                                                
                                                </span>
                                                <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.shipping_method_id"></span> </b>
                                            </td>
                                            <!-- <td style="border: none;text-align:left;">
                                                <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.shipping_method_id"></span> </b>
                                            </td> -->
                                        </tr>
                                        <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;" >
                                                <span style="text-align:left;width:100px;padding-right:45px;">
                                                      <b> Term of Delivery :</b>
                                                </span>
                                                <b> <span t-field="o.invoice_incoterm_id.code"></span> </b>
                                            </td>
                                            <!-- <td style="border: none;text-align:left;">
                                                <b> <span t-field="o.invoice_incoterm_id.code"></span> </b>
                                            
                                            </td> -->
                                        </tr>
                                        <!-- <tr style="border: none;"> 
                                            <td  style="border: none;width:300px;text-align:left;">
                                                <div style="text-align:left;">
                                                    
                                                  <b> Containers: </b>
                                                      
                                                     
                                                
                                                </div>
                                            </td>
                                            <td style="border: none;width:400px;text-align:left;">
                                                <b><span t-field="o.tracking_source" style=" direction:ltr;"></span> </b>
                                            </td>
                                        </tr> -->
                                             <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:72px;">
                                                   <b>  Net Weight :</b> 
                                                     
                                                
                                                </span>
                                                <span> 
                                                    <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_net_weight"></span> </b>
                                                    <b> <span style="padding-left:5px;">KG</span> </b>
                                                </span>
                                            </td>
                                            <!-- <td style="border: none;text-align:left;">
                                                <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_net_weight"></span> </b>
                                                 <b> <span style="padding-left:5px;">KG</span> </b>
                                            </td> -->
                                        </tr>
                                          <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:57px;">
                                                   <b>Payment Term:</b> 
                                                     
                                                
                                                </span>
                                                <!-- <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.payment_term_id"  style="display:inline-block; max-width:150px; word-wrap:break-word; white-space:normal;"></span> </b> -->
                                                <!-- <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.payment_term_id"  style="white-space:normal;"></span> </b> -->
                                                <t t-if="o.invoice_payment_term_id and o.invoice_payment_term_id.line_ids" >
                                                        <b> <span t-field="o.invoice_payment_term_id.line_ids[0].nb_days" style="padding-right:2px;"></span><span t-field="o.invoice_payment_term_id.line_ids[0].delay_type"></span></b>
                                                </t>
                                                


                                            </td>
                                            <!-- <td style="border:none;text-align:left;">
                                                <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.payment_term_id"></span> </b>
                                                

                                            </td> -->
                                        </tr>

                                        <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:63px;">
                                                   <b>Container No:</b> 
                                                     
                                                
                                                </span>
                                                <b> <span t-field="o.tracking_source"></span> </b>
                                            </td>
                                            <!-- <td style="border: none;text-align:left;">
                                                <b> <span t-field="o.tracking_source"></span> </b>
                                                

                                            </td> -->
                                        </tr>

                                        <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:100px;">
                                                   <b>PO No:</b> 
                                                     
                                                
                                                </span>
                                                <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.client_order_ref"></span> </b>
                                            </td>
                                            <!-- <td style="border: none;text-align:left;">
                                             
                                                <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.client_order_ref"></span> </b>
                                                

                                            </td> -->
                                        </tr>

                                        
                                    </table>
                                    </div>
                            </div>
                         

                        
                      

                        <t t-set="filtered_lines" t-value="o.invoice_line_ids.filtered(lambda l: l.display_type == 'product')"/>
                         
                        <table class="no-border" style="border-collapse: collapse; width: 100%;  direction: ltr;font-size:12px; margin-top:20px;font-family:'PT Serif',serif;">
                        <thead>
                            <tr>
                            
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">Product</th>
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">Container</th> -->

                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Size</th> -->
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Variety </th>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Packing </th>
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Carton</th> -->

                            <!-- this code to get unit of mesure in th -->
                             <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">
                                <t t-if="filtered_lines and filtered_lines[0].product_uom_id">
                                    <t t-esc="filtered_lines[0].product_uom_id.name"/>
                                </t>
                                <t t-else="">
                                        
                                                    
                                </t>
                            </th> -->
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Quantity </th> -->
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Count of carton </th>

                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Type of package</th>


                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Unit Price</th>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Total Amount</th>



                           
                                
                             
                        </tr>
                        
                        </thead>

                         <tbody> 
                        
                            <!-- <t t-foreach="o.invoice_line_ids" t-as="line"> -->
                            <t t-foreach="filtered_lines" t-as="line">

                                <tr>
                                 <!-- <t t-set="so_line" t-value="line.sale_line_ids and line.sale_line_ids[0]"/>
                                 
                                 

                                    <td class="td" style="padding: 8px;text-align:center;"><b><t t-esc="so_line.product_template_id.name"/></b></td> -->

                                    <td class="td" style="padding: 8px;text-align:center;">
                                        <b>
                                        <t t-esc="line.product_id.product_tmpl_id.name"/>
                                       </b>
                                    </td>
                                       
                                    
                              
                                    <!-- <td class="td" style="padding: 8px;text-align:center;"><b><t t-esc="line.product_id.name"/></b></td> -->
                                    <!-- <td class="td" style="padding: 8px;text-align:center;"><b><t t-esc="o.picking_id.carrier_id.name"/></b></td> -->

<!--                                    
                                    <t t-if="line.product_id.product_template_attribute_value_ids.attribute_id">
                                       <td class="td" style="padding: 8px;text-align:center;">
                                        <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                            <t t-if="attribute_line.attribute_id.ro_size">
                                              <b>  <t t-esc="attribute_line.name"/></b>
                                            </t>
                                        </t>
                                       
                                    </td>
                                    </t>
                                     <t t-else=""><td class="td" style="padding: 8px;text-align:center;"></td></t> -->

                                      <t t-if="line.product_id.product_template_attribute_value_ids.attribute_id">
                                       <td  class="td" style=" padding: 8px;text-align:center;">
                                        <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                            <t t-if="attribute_line.attribute_id.ro_variety">
                                               <b> <t t-esc="attribute_line.name"/></b>
                                            </t>
                                        </t>
                                       
                                    </td>
                                    </t>
                                     <t t-else=""><td style="padding: 8px;text-align:center;"></td></t>
                                    <td  class="td" style="padding: 8px;text-align:center;"><b><t t-esc="line.product_id.weight"/></b></td>

                                    <td  class="td" style="padding: 8px;text-align:center;"><b><t t-esc="line.quantity"/></b></td>
                                    <td  class="td" style="padding: 8px;text-align:center;"><b><t t-esc="line.product_uom_id.name"/></b></td>


                                    <td class="td" style="padding: 8px; text-align: center;">
                                        <b><span t-field="line.price_unit" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/></b>
                                    </td>
                                    <td class="td" style="padding: 8px; text-align: center;">
                                        <b><span t-field="line.price_subtotal" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/></b>
                                    </td>
                                    
                                    <!-- <td class="td" style="padding: 8px;text-align:center;"><b><t t-esc="line.price_unit"/> <t t-esc="o.currency_id.symbol"/></b></td>
                                     <td class="td" style="padding: 8px;text-align:center;"><b><t t-esc="line.price_subtotal"/> <t t-esc="o.currency_id.symbol"/></b></td>
                                     -->
                                    
                                    
                                    
                                    
                                </tr> 
                            </t>


                             <tr style="height:50px;">
                                    
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                </tr>
                         <tr style="border: 1px solid #000;border-top:1px  solid #000 !important;border-bottom:1px solid #000 !important;">
                         <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="5"> <strong t-field="o.amount_total_words"/>
                         </td>
                         <td style="border: 1px solid #000; padding: 8px;text-align:center;" colspan="1"><b>Total <t t-esc="line.currency_id.currency_unit_label"/></b></td>

                         <td style="border: 1px solid #000; padding: 8px;text-align:center;" colspan="1"><strong t-field="o.amount_total"/></td>
                         </tr>
                    </tbody>
                        
                        </table>


                       <div class="row">
                                    <div class="col-6" style="text-align:left; font-family:'PT Serif',serif; padding-bottom:5px; margin-top:10px;font-family:'PT Serif',serif;">
                                     <strong><p>Bank Details</p></strong>
                                  
                                    
                                    </div>
                                   
                                   
                                     <!-- <div class="col-6" style="text-align:right;">
                                       <strong><p>Stamp</p></strong>
                                    </div> -->
                                   
                                    
                                  </div>
                   
                                    <div style="text-align: left; font-size:12px;">
                                    <div><b>Account Name : </b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_account_name" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                     <div><b>Bank name:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_bank_name" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>Currency: </b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.currency_id" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>Account Number:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_account_number" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>IBAN:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_iban" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>Swift</b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_swift" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>

                                    
                                    
                                    </div>

                                    <div style="text-align: left; font-size:13px;direction:ltr;font-family:'PT Serif',serif;color:#5a855f; margin-top:5px;">
                                        <b>This product is a Global Gap Certified </b>
                                    
                                    </div>
                                    <p style="text-align: left; font-size:13px;direction:ltr;font-family:'PT Serif',serif; color:#5a855f; margin-bottom:40px;"> 
                                       <b> GNN 4063651718362</b>
                                    </p>
                                    
                         
                   

                    <!-- <div style="margin-top:20px;">
                    <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                     </div>    -->
                        

                    <!-- </div> -->
                     
                            <!-- <div class="row">
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>

                                    <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">
                                            <EMAIL>
                                    </a>
                                    
                                    </div>
                                   
                                    
                                     <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/world-wide-web.png'" alt="web" style="height:25px;wedth:25px;"/>

                                      <a t-att-href="'http://www.xeedcorp.com/'" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">
                                                            www.xeedcorp.com
                                     </a>
                                    </div>
                                   
                                  
                                     <div class="col-4" style="text-align:center;font-family:'PT Serif',serif;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/telephone.png'" alt="phone" style="height:25px;wedth:25px;"/>
                                     
                                     <strong><span style="color:#5a855f;">+2010 915 56 556</span></strong>
                                    </div>
                             </div> -->
                    
        <!-- </div> -->


                        <!-- <div class="oe_structure"></div>
                        <p style="page-break-before:always;"></p> -->



              
                       
                           
                        </div>
                        </t>
                     <t t-else="">
                     </t>
                    </t>
                </t>
            </t>
                
        </template>

        </odoo>