
from odoo import models, fields, api, _, exceptions


class WizardPayslip(models.TransientModel):
    _name = 'wizard.payslip'
    _description = 'wizard All Payslips report'
    
    ro_date_from =fields.Date(string="From")
    ro_date_to = fields.Date(
        string='To')
    
    
    def action_open_report(self):
        rows = []
        employee = self.env['hr.employee'].search([])
        
        employees_period_date=self.env['hr.payslip'].search([('date_from', '=', self.ro_date_from),('date_to', '=', self.ro_date_to)])
        
        if self.ro_date_from:
            month = self.ro_date_from.month
            year = self.ro_date_from.year
        else:
            month = False
            year = False

        
        # bloom employees
        employee_is_bloom =employee.filtered(lambda x:x.department_id.ro_is_bloom == True) 
        employee_is_bloom_pay_slip = self.env['hr.payslip'].search([
            '&',
            ('employee_id', 'in', employee_is_bloom.ids),
            ('id', 'in', employees_period_date.ids)
            ])
        employee_is_bloom_net_wage =sum(employee_is_bloom_pay_slip.mapped('net_wage'))
        
        
        
        # administration employees
        employee_is_not_bloom =employee.filtered(lambda x:x.department_id.ro_is_bloom == False)
        employee_is_not_bloom_pay_slip = self.env['hr.payslip'].search([
            '&',
            ('employee_id', 'in', employee_is_not_bloom.ids),
            ('id', 'in', employees_period_date.ids)
            
            ])
        employee_is_not_bloom_net_wage =sum(employee_is_not_bloom_pay_slip.mapped('net_wage'))
        
        
        pay_cash_employees=employee.filtered(lambda x:not x.bank_account_id)
        employee_pay_cash_pay_slip= self.env['hr.payslip'].search([
            '&',
            ('employee_id', 'in', pay_cash_employees.ids),
            ('id', 'in', employees_period_date.ids)
            ])
        employee_pay_cash_pay_slip_net_wage =sum(employee_pay_cash_pay_slip.mapped('net_wage'))
        
        
        pay_bank_employees=employee.filtered(lambda x: x.bank_account_id)
        employee_pay_bank_pay_slip= self.env['hr.payslip'].search([
            '&',
            ('employee_id', 'in', pay_bank_employees.ids),
            ('id', 'in', employees_period_date.ids)
            ])
        employee_pay_bank_pay_slip_net_wage =sum(employee_pay_bank_pay_slip.mapped('net_wage'))
       
        
       
        # for row in employee:  
        myrow = {
            'bloom_total_net_wages': employee_is_bloom_net_wage,
            'administration_total_net_wages': employee_is_not_bloom_net_wage,
            'employee_pay_cash_net_wages': employee_pay_cash_pay_slip_net_wage,
            'employee_pay_bank_net_wages': employee_pay_bank_pay_slip_net_wage,
            'month':month,
            'year':year
        }
        rows.append(myrow)
        

        data = {
            'rows': rows,
        }
        return self.env.ref('ro_report_for_all_payslip.action_print_all_payslips_report').report_action(self,data=data)
    