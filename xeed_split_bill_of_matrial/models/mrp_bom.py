from odoo import models

class MrpBom(models.Model):
    _inherit = 'mrp.bom'
        
    def split_bom_lines(self):
        for bom in self:
            new_lines = []
            for line in bom.bom_line_ids:
                if line.product_qty > 1 and line.product_id.split_checkbox == True:
                    for i in range(int(line.product_qty) - 1):
                        new_lines.append((0, 0, {
                            'product_id': line.product_id.id,
                            'product_qty': 1,
                            'product_uom_id': line.product_uom_id.id,
                            'sequence': line.sequence,
                        }))
                    line.product_qty = 1
            if new_lines:
                bom.write({'bom_line_ids': new_lines})