from odoo import models, fields, api

class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    ro_inv_product_size = fields.Many2one('product.tag', string='Size')


    # def get_stock_move_lines(self):
    #     soline = self.sale_line_ids[0]
    #     if soline:
    #         stock_moves = self.env['stock.move'].search([('sale_line_id', '=', soline.id)])
    #         stock_move_lines = stock_moves.mapped('picking_id.ro_cargo_line_ids')
    #         return stock_move_lines
    #     return False
