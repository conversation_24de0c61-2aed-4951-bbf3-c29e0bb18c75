from odoo import models, fields, api

class PurchaseLogistic(models.Model):
    _inherit = 'logistic.purchase'
    
    
 
    ro_print_check_box = fields.Boolean(string='check box')

    
  
class AccountMoveLine(models.Model):
    _inherit = 'account.move'

    # new_line_price = fields.Float(string='line price', compute='_compute_new_line_price')

    new_price = fields.Float(string='New Price', compute='_compute_new_price',)

    @api.depends('invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids.ro_print_check_box', 'invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids.unit_price')
    def _compute_new_price(self):
        for line in self:
            lines = line.invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids
            filtered_lines = lines.filtered(lambda purchase: purchase.ro_print_check_box)
             
            if filtered_lines :
                # invoice_display_type=line.invoice_line_ids.filtered(lambda display: display.display_type == 'product')
                total_lines = len(line.invoice_line_ids.filtered(lambda l: l.display_type == 'product'))
                unit_prices = filtered_lines.mapped('unit_price')

                if unit_prices:
                    line.new_price = sum(unit_prices) / total_lines
                else:
                    line.new_price = 0.0
            else:
                line.new_price = 0.0


   
    # @api.depends('invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids.ro_print_check_box', 'invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids.unit_price')
    # def _compute_new_price(self):
    #     for line in self:
    #         if line.invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids.ro_print_check_box:
    #             total_lines = len(line.invoice_line_ids)
    #             line.new_price = sum(line.invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids.mapped('unit_price')) / total_lines
    #         else:    
    #             line.new_price = 0.0


    # @api.depends('new_price', 'invoice_line_ids.price_unit')
    # def _compute_new_line_price(self):
    #     for line in self:
    #         if line.invoice_line_ids.price_unit:
    #             line.new_line_price = sum(unit_price - line.new_price for unit_price in line.invoice_line_ids.mapped('price_unit'))
    #         else:
    #             line.new_line_price = 0.0

# class AccountMoveLine(models.Model):
#     _inherit = 'account.move.line'

  

    # ro_new_price = fields.Float(string='New Price',related='move_id.new_line_price')
    # new_line_price = fields.Float(string='line price', compute='_compute_new_line_price')
    # new_sub_total = fields.Float(string='new sub total', compute='_compute_sub_total')



    # @api.depends('move_id.new_price', 'price_unit')
    # def _compute_new_line_price(self):
    #     for line in self:
    #         if line.price_unit:
    #             line.new_line_price = line.move_id.new_price - line.price_unit
    #         else:
    #             line.new_line_price = 0.0

    # @api.depends('new_line_price','quantity')
    # def _compute_sub_total(self):
    #     for line in self:
            
    #         line.new_sub_total = line.quantity * line.new_line_price
                     


   

  
    

  
    