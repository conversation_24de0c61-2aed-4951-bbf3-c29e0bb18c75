<?xml version="1.0" encoding="utf-8"?>
<odoo>

   <data>
     <record id="account_move_form_custom" model="ir.ui.view" >
            <field name="name">account.move.form.custom</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='payment_reference']" position="after">
                 <!-- <field name="suitable_picking_ids" invisible="1"/> -->

                 <field name="new_price" string="new price" invisible="1"></field>
                 <!-- <field name="tracking_source" string="Container No"></field> -->

                
                </xpath>

                 <xpath expr="//field[@name='quantity']" position="before">

                 <field name="ro_inv_product_size" string="size"></field>
                

                
                </xpath>
                <!-- <xpath expr="//field[@name='price_unit']" position="after">
                 

                 <field name="new_line_price" string="line price"></field>
                 <field name="new_sub_total" string="new sub total"></field>

                 
                

                
                </xpath> -->

                
              
                
            </field>
        </record>

  </data>

</odoo>



 
