<?xml version="1.0" encoding="utf-8"?>
<odoo>
<record id="paperformat_final" model="report.paperformat">
        <field name="name">Survey Certification final</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">50</field>
        <field name="margin_bottom">30</field>
        <field name="margin_left">10</field>
        <field name="margin_right">10</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">40</field>
        <field name="disable_shrinking" eval="True"/>
        <field name="dpi">112</field>
    </record>


  

        <template id="invoice_final_comerical_external_layout">
      
        <div t-attf-class="header no-border-tbody" t-att-style="report_header_style">

             <style>
                                    .no-border-tbody tbody{border:0px !important;}

                                    .no-border{border:0px !important;}
                                    .no-border tbody td{ 
                                        border-top:0px !important;
                                        border-bottom:0px !important;                                        }
                                    .no-border tbody tr{border:0px !important;}
                                    <!-- .add-border tbody tr{border:1px !important;}
                                    .add-border tbody td{border:1px !important;} -->

                                    .td {
                                            border-top:border:0px !important;
                                            border-bottom:border:0px !important;
                                            border-left: 1px solid black !important;
                                            border-right: 1px solid black !important;
                                          
                                        }

                            </style>
            <!-- <div class="row">
           
               
                <div>
                      
                     <img t-att-src="'/ro_final_commerical_invoice_print/static/src/img/logoh.png'" alt="Company Logo" style="float: left; margin-right: 10px;"/>
                </div>
               
            </div> -->

            <table class="no-border" style="border:none;margin-top:10px;font-family:'PT Serif',serif;font-size:16;">
                <td class="no-border  col-3" style="border:none;" >
                 <img t-att-src="'/ro_final_commerical_invoice_print/static/src/img/logoh.png'" alt="Company Logo" style="float: left; margin-right: 10px;" class="no-border"/>
           
               </td>
               <td class="no-border col-9" style="text-align:center;border:none;margin-left:20px;padding-left:100px;" >

                      
                <b class="no-border" style="font-size:16;padding-bottom:10px;font-family:'PT Serif',serif;"> Final Invoice</b>
              
            
             </td>
             </table>


        </div>
 
        <!-- <div t-attf-class="article o_report_layout_standard o_company_#{o.company_id.id}_layout {{  'o_report_layout_background' if o.company_id.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % o.company_id.layout_background_image.decode('utf-8') if o.company_id.layout_background_image and o.company_id.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if o.company_id.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')"> -->
        <!-- <div t-attf-class="article o_report_layout_standard " t-attf-style="background-image: url('/ro_final_commerical_invoice_print/static/src/img/water_mark.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">    -->
            <div class="pt-5">
           
                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
        <!-- </div> -->
        <div t-attf-class="footer">
            <div style="margin-top:10px;">
                <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                 </div> 

                         <div class="row">
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_final_commerical_invoice_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>

                                    <!-- <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color:#5a855f; text-decoration: underline;"> -->
                                           <strong>  <span  style="color:#5a855f; text-decoration: underline;"><EMAIL></span></strong> 
                                    <!-- </a> -->
                                    <!-- <b style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;"><EMAIL></b> -->
                                    
                                    </div>
                                   
                                
                                     <div class="col-4" style="text-align:center; font-family:'PT Serif',serif;">
                                    <img t-att-src="'ro_final_commerical_invoice_print//static/src/img/world-wide-web.png'" alt="web" style="height:25px;wedth:25px;"/>

                                      <!-- <a t-att-href="'http://www.xeedcorp.com/'" style="color: #5a855f; text-decoration: underline;"> -->
                                                     <strong>      <span  style="color:#5a855f; text-decoration: underline;">www.xeedcorp.com </span> </strong> 
                                     <!-- </a> -->
                                  
                                    </div>
                                   
                                   
                                     <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_final_commerical_invoice_print//static/src/img/telephone.png'" alt="phone" style="height:25px;wedth:25px;"/>

                                     <strong><span style="color:#5a855f;">+2010 915 56 556</span></strong>
                                    </div>
                          </div>
            </div>
            <!-- </div> -->

        
    </template>

       <record id="action_final_commerical_invoice_report" model="ir.actions.report">
            <field name="name">Final Invoice</field>
            <field name="model">account.move</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_final_commerical_invoice_print.final_commerical_report_template</field>
            <field name="report_file">ro_final_commerical_invoice_print.final_commerical_report_template</field>
             <field name="paperformat_id" ref="ro_final_commerical_invoice_print.paperformat_final"/>
            <field name="print_report_name">'Final Invoice'</field>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_type">report</field>
        </record>


         <template id="final_commerical_report_template">
            <t t-call="web.html_container">
                <t t-call="ro_final_commerical_invoice_print.invoice_final_comerical_external_layout">
                     <link rel="preconnect" href="https://fonts.googleapis.com"/>
                    <link rel="preconnect" href="https://fonts.gstatic.com"/>
                    <link href="https://fonts.googleapis.com/css2?family=PT+Serif:ital,wght@0,400;0,700;1,400;1,700" rel="stylesheet"/>
                    <t t-foreach="docs" t-as="o">
                    <t t-if="o.line_ids[0].sale_line_ids">

                        <div class="page">
                        <div t-attf-class="article o_report_layout_standard " t-attf-style="background-image: url('/ro_final_commerical_invoice_print/static/src/img/xeedbg.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">   
                           <!-- <div class="text-center no-border" style="font-size:18; padding-bottom:30px;font-family:'PT Serif',serif;"> <b>Final Invoice</b></div> -->
                          <div class="row" style="width: 100%; display: flex; justify-content: space-between;">
                                 <style>
                                    .no-border-tbody tbody{border:0px !important;}

                                    .no-border{border:0px !important;}
                                    .no-border tbody td{ 
                                        border-top:0px !important;
                                        border-bottom:0px !important;                                        }
                                    .no-border tbody tr{border:0px !important;}
                                    .add-border tbody tr{border:1px !important;}
                                    .add-border tbody td{border:1px !important;}

                                    .td {
                                            border-top:border:0px !important;
                                            border-bottom:border:0px !important;
                                            border-left: 1px solid black !important;
                                            border-right: 1px solid black !important;
                                          
                                        }

                                   </style>
                                    <div class="col-7 no-border" style="text-align: left; font-size:12px;">
                                    <strong><div style="font-family:'PT Serif',serif;">XEED FOR IMPORT AND EXPORT</div></strong>
                                    <div style="font-size:14px;font-family:'PT Serif',serif;">Building No. 37, Walk of Cairo Trade Center,</div>
                                    <div style="font-size:14px;font-family:'PT Serif',serif;">Kilo 38, Cairo-Alexandria Desert Road -Sheikh Zayed,Egypt</div>

                                    </div>
                                    <div class="col-5" style="text-align: right;font-size:12px;">
                                    <!-- <p style="text-align:left;font-size:16px;font-family:'PT Serif',serif;"><b>Final Invoice</b></p> -->

                                    <table  class="no-border-tbody" style="border-collapse: collapse; width: 85%;margin-right:10px;font-family:'PT Serif',serif;">
                                        <tr style="border: none;">
                                            <td style="border: none;width:90px;text-align:left;font-size:12px;">
                                                <span style="text-align:left;">
                                                
                                                   <b> Invoice NO : </b>
                                                
                                                </span>
                                            </td>
                                            <td style="border: none;width:100px;text-align:left;font-size:12px;">
                                                <b><span t-field="o.name"></span> </b>
                                            </td>
                                            </tr>
                                            <tr style="border: none;">
                                            <td style="border: none;width:90px;text-align:left;font-size:12px;">
                                                <span style=" text-align:left;">
                                                
                                                  <b> Invoice Date : </b>
                                                
                                                </span>
                                            </td>
                                            <td style="border: none;width:120px;text-align:left;font-size:12px;">
                                                <b>
                                                        <!-- <span t-field="o.invoice_date"></span>  -->
                                                        <span t-esc="o.invoice_date.strftime('%d-%b-%Y')if o.invoice_date else ''"></span>
                                                </b>
                                            </td>
                                            </tr>
                                            <tr style="border: none;">
                                            <td style="border: none;width:90px;text-align:left;font-size:12px;">
                                                <span style=" text-align:left;">
                                                
                                                  <b>  BL Number: </b>
                                                
                                                </span>
                                            </td>
                                            <td style="border: none;width:120px;text-align:left;font-size:12px;">
                                                <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.bl_number"></span> 
                                                </b>
                                            </td>
                                            </tr>

                                            <tr style="border: none;">
                                                <td style="border: none;width:90px;text-align:left;font-size:12px;">
                                                    <span style=" text-align:left;">
                                                         <b> SO NO. : </b>            
                                                    </span>
                                                </td>
                                                <td style="border: none;width:120px;text-align:left;font-size:12px;padding-bottom:30px;">
                                                    <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.name"></span>
                                                            

                                                    </b>
                                                </td>
                                                
                                            </tr>   
                                            <!-- <tr style="border: none;"> 
                                            <td style="border: none; direction: ltr;">
                                                <p style="text-align:left;">
                                                
                                                      <b>  Due Date : </b><span t-field="o.invoice_date_due" style=" direction:ltr;"></span> 
                                                
                                                </p>
                                            </td>
                                        </tr> -->
                                    
                                    </table>
                                    </div>
                            </div>
                            
                         <b><hr style="border: 1px solid #000; width:100%;font-weight: bold;"/></b>
                          <div class="row" style="width: 100%; display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <div class="col-7" style="text-align: left;">
                                <table class="no-border-tbody" style="border-collapse: collapse; font-size:13px;font-family:'PT Serif',serif;">
                                    <tr style="border: none;">
                                        <td style="border: none;font-size:12px;">
                                            <span style=" text-align:left;">

                                                <b><u>Bill To: </u> </b>
                                            </span>
                                          
                                             <div style="width:300px;">
                                               <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.name"/>
                                                        <br/>
                                                        
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.street"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.city">
                                                            <span>,</span>
                                                            
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.city"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.zip">
                                                            <span>,</span>
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.zip"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.country_id.name">
                                                            <span>,</span>
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.country_id.name"/>
                                                    </b>
                                            </div>                                                      
                                            <!-- <div><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.notify_id"></span></b></div>
                                            <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.notify_id.street"></span></b></p> -->
                                          

                                           
                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b> <span t-field="o.partner_id.name"></span></b>
                                        </td> -->

                                    </tr>

                                      <tr style="border: none;">
                                        
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:150px;padding-right:49px; ">
                                                <b>
                                                    Vat Number: 
                                                </b>
                                              


                                            </span>
                                            <b>   
                                                <span t-field="o.partner_id.vat"></span>
                                            </b>
                                        </td>
                                        <!-- <td style="border: none;width:100px;text-align:left;font-size:13px;">
                                            <b>   
                                                <span t-field="o.partner_id.vat"></span>
                                            </b>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style=" text-align:left;width:150px; padding-right:80px; ">
                                                <b>
                                                    Origin : 
                                                </b>
                                              


                                            </span>
                                            <b><span>Egypt</span></b>
                                        </td>
                                        <!-- <td style="border: none;width:100px;text-align:left;font-size:13px;">
                                            <b><span>Egypt</span></b>
                                        </td> -->
                                    </tr>

                                      <tr style="border: none;">
                                                
                                                <td style="border: none;text-align:left; font-size:12px;">
                                                    <span style="width:150px;padding-right:85px;">
                                                        <b>
                                                        Agent:
                                                        </b>
                                                    </span> 
                                                    <b>
                                                      <span t-field="o.line_ids[0].sale_line_ids[0].order_id.agent_id"></span>
                                                    </b>   
                                                </td>
                                                 <!-- <td style="border: none;width:150px;text-align:left;">
                                                    <b>
                                                      <span t-field="o.line_ids[0].sale_line_ids[0].order_id.agent_id"></span>
                                                    </b>
                                                
                                                </td> -->
                                               

                                            </tr>

                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style=" text-align:left;width:150px;padding-right:17px;">

                                                <b> Port of Departure :</b>
                                                

                                            </span>
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.loading_port"></span>
                                            </b>
                                        </td>
                                        <!-- <td style="border: none;width:150px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.loading_port"></span>
                                            </b>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:80px;">
                                                <b> Vessel:</b>
                                                

                                            </span>
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.vessel_name"></span>
                                            </b>
                                        </td>
                                         
                                        <!-- <td style="border: none;width:150px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.vessel_name"></span>
                                            </b>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:15px;">
                                                <b>  Number of Pallets :</b>


                                                <!-- Due Date :<span t-field="o.invoice_date_due" style=" direction:ltr;"></span>  -->

                                            </span>
                                            <t t-foreach="o.invoice_line_ids" t-as="line">


                                                <!-- <t t-set="ro_cargo_line_ids" t-value="line.get_stock_move_lines()"/> -->
                                                <!-- <t t-set="pallet_count" t-value="pallet_count+len(ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/> -->
                                            
                                            <!-- </t> -->
                                          
                                            <b><span t-esc="len(o.picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/></b>
                                             </t>
                                        </td>
                                        
                                        <!-- <td style="border: none;width:150px;text-align:left;font-size:13px;">
                                          
                                            <t t-foreach="o.invoice_line_ids" t-as="line">


                                          
                                            <b><span t-esc="len(o.picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/></b>
                                             </t>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:50px;">


                                                <b>PO Number :</b>
                                                

                                            </span>
                                            <b>
                                                    <span t-field="o.ref"></span>
                                            </b>
                                        </td>
                                        <!-- <td style="border: none;width:150px;text-align:left;font-size:13px;">
                                            <b>
                                                    <span t-field="o.ref"></span>
                                            </b>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:42px;">
                                                <b> Gross Weight:</b>
                                            </span>
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_gross_weight"></span>
                                            </b>
                                            <b> <span style="padding-left:5px;">KG</span> </b>
                                        </td>
                                        <!-- <td style="border: none;width:100px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_gross_weight"></span>
                                            </b>
                                            <b> <span style="padding-left:5px;">KG</span> </b>
                                        </td> -->
                                    </tr>



                                    <!-- <tr style="border: none;">
                                        <td style="border: none; direction: ltr;">
                                            <p style="text-align:left;">




                                                <b> ETD :</b>
                                                <span t-field="o.line_ids.sale_line_ids.order_id.eta_date" style=" direction:ltr;"></span>

                                            </p>
                                        </td>
                                    </tr> -->

                                    <!-- <tr style="border: none;">
                                        <td style="border: none; direction: ltr;">
                                            <p style="text-align:left;">
                                                <b>  Loading Date:</b>
                                                <span t-field="o.line_ids.sale_line_ids.order_id.loading_date" style=" direction:ltr;"></span>



                                            </p>
                                        </td>
                                    </tr> -->


                                </table>
                            </div>
                            <div class="col-5" style="margin-right:70px;font-size:12px;">
                                <table class="no-border-tbody" style="border-collapse: collapse; width: 100%; text-align:left; font-family:'PT Serif',serif;">
                                    <!-- <tr style="border: none;">
                                        <td style="border: none;">
                                            <span style=" text-align:left;">

                                                <b><u>Ship To:</u></b>

                                            </span>
                                            <p style="width:200px;text-align:left;font-size:13px;">
                                              
                                               
                                            <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id"></span></b></p>

                                            </p>
                                        </td>
                                    </tr> -->

                                     <tr style="border: none;">
                                        <td style="border: none; font-size:12px;">
                                            <span style=" text-align:left;">

                                                <b><u>Ship To: </u> </b>
                                            </span>

                                        <div style="width:300px;">
                                                    <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.name"/>
                                                        <br/>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.street"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.city">
                                                            <span>,</span>
                                                        </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.city"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.zip">
                                                            <span>,</span>
                                                        </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.zip"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.country_id.name">
                                                            <span>,</span>
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.country_id.name"/>
                                                    </b>

                                         </div>           
                                           
                                            <!-- <div><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id"></span></b></div>
                                            <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id.street"></span></b></p> -->
                                          


                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b> <span t-field="o.partner_id.name"></span></b>
                                        </td> -->

                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:50px;">
                                                <b> Destination:</b>
                                                

                                                <!-- Invoice Date :<span t-field="o.invoice_date" style=" direction:ltr;"></span>  -->

                                            </span>
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.ro_country"></span>
                                            </b>
                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.ro_country"></span>
                                            </b>

                                        </td> -->
                                    </tr>

                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:32px;">

                                                <b>Port of Arrivial: </b>
                                               

                                            </span>
                                            <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.destination_port"></span></b>
                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.destination_port"></span></b>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:14px;">

                                                <b>Shipping Method : </b>

                                            </span>
                                              <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.shipping_method_id"></span>
                                            </b>
                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.shipping_method_id"></span>
                                            </b>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px; padding-right:17px;">
                                                <b> Term of Delivery :</b>
                                                

                                            </span>
                                            <b>
                                                <span t-field="o.invoice_incoterm_id.code"></span>
                                            </b>
                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.invoice_incoterm_id.code"></span>
                                            </b>
                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:33px;">

                                                <b> Container No : </b>

                                                <!-- Term of Delivery :<span t-field="o.invoice_incoterm_id" style=" direction:ltr;"></span>  -->

                                            </span>
                                            <b>
                                                <span t-field="o.tracking_source"></span>
                                                
                                            </b>

                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.tracking_source"></span>
                                                
                                            </b>

                                        </td> -->
                                    </tr>
                                    <tr style="border: none;">
                                        <td style="border: none;text-align:left;font-size:12px;">
                                            <span style="text-align:left;width:100px;padding-right:48px;">
                                                <b>  Net Weight : </b>

                                            </span>
                                             <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_net_weight"></span>

                                            </b>
                                            <b> <span style="padding-left:5px;">KG</span> </b>
                                        </td>
                                        <!-- <td style="border: none;width:200px;text-align:left;font-size:13px;">
                                            <b>
                                                <span t-field="o.line_ids[0].sale_line_ids[0].order_id.total_ro_computed_net_weight"></span>

                                            </b>
                                            <b> <span style="padding-left:5px;">KG</span> </b>
                                        </td> -->
                                    </tr>

                                </table>
                            </div>
                        </div>      
                      


                        <!-- <table class="text-center" style="border-collapse: collapse; width: 100%;  direction: ltr; margin-top:20px; font-family:'PT Serif',serif;"> -->
                        <!-- <t t-set="filtered_lines" t-value="o.invoice_line_ids.filtered(lambda l: not l.display_type)"/> -->
                        <t t-set="filtered_lines" t-value="o.invoice_line_ids.filtered(lambda l: l.display_type == 'product')"/>



                        <table  class="no-border" style="border-collapse: collapse; width: 100%;margin-right:10px;font-family:'PT Serif',serif; text-align:left;font-size:13px; font-weight:bold;">
                        
                        <thead>
                            <tr>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">product</th>
                            
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">Item</th> -->
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Size</th> -->
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Variety </th>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Packing </th>
                           
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Quantity</th> -->
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Count of carton</th>

                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;"> Type of package</th>

                            <!--///////////////////// to change header to be like name of  unit messure/////////////////// -->
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">
                                <t t-if="filtered_lines and filtered_lines[0].product_uom_id">
                                    <t t-esc="filtered_lines[0].product_uom_id.name"/>
                                </t>
                                <t t-else="">
                                        
                                                    
                                </t>
                            </th> -->
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Unit Price</th>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Total Amount</th>



                           
                                
                             
                        </tr>
                        
                        </thead>

                         <tbody style="border: 1px solid #000;" > 
                        
                            <t t-set="total" t-value="0"/>
                            <!-- <t t-foreach="o.invoice_line_ids" t-as="line"> -->
                            <t t-foreach="filtered_lines" t-as="line">

                            
                                <tr style="border: 1px solid #000;">
                                 <!-- <t t-set="so_line" t-value="line.sale_line_ids and line.sale_line_ids[0]"/>
                                 
                                 

                                    <td class="td" style="padding: 8px;text-align:left;"><t t-esc="so_line.product_template_id.name"/></td> -->
                                    <td class="td" style="padding: 8px;text-align:left;"><t t-esc="line.product_id.product_tmpl_id.name"/></td>
                              
                                   
                                   <t t-if="line.display_type == 'product'">
                                        <!-- <td class="td" style="padding: 8px;text-align:left;"><t t-esc="line.product_id.name"/></td> -->
                                        <!-- <t t-if="line.product_id.product_template_attribute_value_ids.attribute_id">
                                            <td class="td" style="border: 1px solid #000; padding: 8px;text-align:center;">
                                                <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                                    <t t-if="attribute_line.attribute_id.ro_size">
                                                        <t t-esc="attribute_line.name"/>
                                                    </t>
                                                </t>
                                            
                                            </td>
                                        </t> -->
                                        <!-- <t t-else=""><td style="border: 1px solid #000; padding: 8px;text-align:center;"></td></t> -->

                                        <t t-if="line.product_id.product_template_attribute_value_ids.attribute_id">
                                        <td class="td" style="padding: 8px;text-align:left;">
                                            <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                                <t t-if="attribute_line.attribute_id.ro_variety">
                                                    <t t-esc="attribute_line.name"/>
                                                </t>
                                            </t>
                                        
                                            </td>
                                    </t>
                                     <t t-else=""><td class="td" style="padding: 8px;text-align:center;"></td></t>
                                    <td class="td" style="padding: 8px;text-align:left;"><t t-esc="line.product_id.weight"/></td>

                                    <td class="td" style="padding: 8px;text-align:left;"><t t-esc="line.quantity"/></td>
                                    <td class="td" style="padding: 8px;text-align:left;"><t t-esc="line.product_uom_id.name"/></td>


                                    <!-- <td class="td" style="padding: 8px;text-align:left;"> <t t-esc="line.display_type or 'No display_type'"/></td> -->

                                     
                                     
                                    <!-- <t t-if="line.sale_line_ids.order_id.logistic_purchase_ids.ro_print_check_box"> -->
                                    <t t-if="line.sale_line_ids.order_id.logistic_purchase_ids.filtered(lambda purchase: purchase.ro_print_check_box == True)">
                                        <t t-foreach="o.invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids.filtered(lambda purchase: purchase.ro_print_check_box == True)" t-as="logistic_lines">
                                            <td class="td" style="padding: 8px;text-align:left;"> <t t-esc="round(line.price_unit - (logistic_lines.unit_price / (line.quantity or 1)), 4)" t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                            </td>
                                            

                                        <!-- recalculate total amount -->
                                            <td class="td" style="padding: 8px;"><t t-esc="round((line.quantity *( line.price_unit - (logistic_lines.unit_price / (line.quantity or 1)))),4)" t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align:right;"/>          
                                            </td> 
                                        </t>  
                                      <!-- <td class="td" style="padding: 8px;text-align:left;"><t t-esc="round((line.price_unit-line.move_id.new_price),4)"/>     
                                      </td> 
                                       <td class="td" style="padding: 8px;"><t t-esc="o.currency_id.symbol" style="text-align:left;"/><t t-esc="round((line.quantity * (line.price_unit-line.move_id.new_price)),4)" style="text-align:right;"/>          
                                      </td>  -->
                                    </t> 
                                    <t t-else="">
                                      <!-- <td class="td" style=" padding: 8px;text-align:left;"><t t-esc="round(line.price_unit,4)"/>  -->
                                  
                                      <td class="td" style=" padding: 8px;text-align:left;"><t t-esc="round(line.price_unit,4)"  t-options='{"widget": "monetary", "display_currency": o.currency_id}' /> 

                                      </td>  
                                      <td  class="td" style="padding: 8px;"><t t-esc="round(line.price_subtotal,4)" t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align:right;"/>          

                                       
                                            
                                       </td>
                                    </t>
                                            
                                    <!-- <td style="border: 1px solid #000; padding: 8px;text-align:center;"><t t-esc="line.new_line_price"/></td>
                                     <td style="border: 1px solid #000; padding: 8px;text-align:center;"><t t-esc="line.new_sub_total"/></td>
                                     -->
                                    
                                    </t>
                                    
                                    
                                </tr> 
                                

                            <t t-set="total" t-value="total + (line.quantity * (line.price_unit - line.move_id.new_price))"/>

                            </t>
                        <t t-foreach="o.invoice_line_ids.sale_line_ids.order_id.logistic_purchase_ids" t-as="logistic_lines">
                          <t t-if="logistic_lines.filtered(lambda purchase: purchase.ro_print_check_box == True)">

                                <tr style="border: 1px solid #000;">
                                 <td class="td no-border"  style=" padding: 8px;text-align:left;"><t t-esc="logistic_lines.product_template_id.name"/></td>
                                <!-- <td class="td no-border"  style=" padding: 8px;text-align:left;"><t t-esc="logistic_lines.product_id.name"/></td> -->
                                <td class="td" style=" padding: 8px;text-align:left;" >
                                </td>
                                <td class="td" style="padding: 8px;text-align:left;" >
                                </td>
                                <td class="td" style="padding: 8px;text-align:left;" ><t t-esc="sum(o.invoice_line_ids.mapped('quantity'))"/>
                                </td>
                                <td class="td" style="padding: 8px;text-align:left;" ><t t-esc="logistic_lines.product_template_id.uom_id.name"/>
                                </td>
                                <!-- ///////////////////////////////////////////////////////////////// -->
                                          <!--recalculate price_unit  -->
                                 <!-- <td class="td" style=" padding: 8px;text-align:left;" ><t t-esc="round(o.new_price,4)"/>
                                 </td>    -->
                                 <td class="td" style="padding: 8px; text-align: left;">
                                    <t t-esc="round(logistic_lines.unit_price  / (sum(o.invoice_line_ids.mapped('quantity') or 1)), 4)" t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align:right;" />
                                
                                
                                
                                </td>
                                <!-- <td class="td" style=" padding: 8px;" ><t t-esc="o.currency_id.symbol" style="text-align:left;"/><t t-esc="round(o.new_price*sum(o.invoice_line_ids.mapped('quantity')),4)" style="text-align:right;"/> -->

                                <!-- recaculate total amount for logistics -->
                                <td class="td" style=" padding: 8px;" ><t t-esc="round(logistic_lines.unit_price,4)"  t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align:right;"/>

                                                   

                                
                                </td>
                                <!--<td style="border: 1px solid #000; padding: 8px;text-align:center;" colspan="2"><t t-esc="o.amount_total"/></td> --> 
                              
                              </tr>
                           </t>
                        </t>   

                        <!-- <tr>
                            <td class="td" style=" padding: 8px;text-align:center; height:30px;">
                            
                            </td>
                            <td class="td" style=" padding: 8px;text-align:center; height:30px;">
                            
                            </td>
                            <td class="td" style=" padding: 8px;text-align:center; height:30px;">
                            
                            </td>
                            <td class="td" style=" padding: 8px;text-align:center; height:30px;">
                            
                            </td>
                            <td class="td" style=" padding: 8px;text-align:center; height:30px;">
                            
                            </td>
                            <td class="td" style=" padding: 8px;text-align:center; height:30px;">
                            
                            </td>
                        </tr> -->
                        
                        <tr style="height:40px;">
                                    
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                     <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td>
                                    <!-- <td class="td" style="padding: 8px;text-align:left;">
                                    
                                    </td> -->
                                   
                                </tr>
                               
                       
                       
                        <tr class="add-border" style="border: 1px solid #000;border-top:1px  solid #000 !important;border-bottom:1px solid #000 !important;">
                        <td style="border: 1px solid #000; padding: 8px;text-align:left; font-size:13px;" colspan="5"> <strong t-field="o.amount_total_words"/>
                         </td>
                         <!-- <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="4">Total
                         </td> -->
                         <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="1"> <b>Total <t t-esc="o.currency_id.currency_unit_label"/> </b>
                         </td>
                         <t t-if="o.new_price">

                         <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="1">
                         <b>
                         <t t-esc="total+o.new_price*sum(o.invoice_line_ids.mapped('quantity'))" t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align:right;"/></b></td>
                         </t>
                         <t t-else="">
                                <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="1"><b><t t-esc="round(o.amount_total,4)" t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align:right;"/></b></td>
                         
                         </t>
                         </tr>  

                                                

                    </tbody>
                        
                        </table>


                        <!-- <div t-attf-class="footer"> -->
                        <t t-if="o.narration">
                             <div style="text-align:left; font-family:'PT Serif',serif; margin-top:10px;font-size:14;">
                                 <b> Note: </b><span t-field="o.narration"></span>
                                </div>
                        </t>
                       
                              
                                <div class="row">
                                    <div class="col-6" style="text-align:left; font-family:'PT Serif',serif; padding-bottom:5px;margin-top:10px;">
                                     <strong><p>Bank Details </p></strong>
                                  
                                    
                                    </div>
                                   
                                   
                                     <!-- <div class="col-6" style="text-align:right;">
                                       <strong><p>Stamp</p></strong>
                                    </div> -->
                                   
                                    
                                  </div>
                   
                                    <div style="text-align: left; font-size:13px;">
                                    <span><b>Account Name : </b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_account_name" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </span>
                                     <div><b>Bank name:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_bank_name" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>Currency: </b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.currency_id" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>Account Number:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_account_number" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>IBAN:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_iban" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                      <div><b>Swift</b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_swift" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>

                                    
                                    
                                    </div>

                                    <div style="text-align: left; font-size:13px;direction:ltr;font-family:'PT Serif',serif;color:#5a855f; margin-top:5px;">
                                       <b> This product is a Global Gap Certified </b>
                                    
                                    </div>
                                    <p style="text-align: left; font-size:13px;direction:ltr;font-family:'PT Serif',serif; color:#5a855f; margin-bottom:40px;"> 
                                       <b> GNN 4063651718362 </b>
                                    </p>
                                    
                         
                   

            
        <!-- </div> -->


                        <!-- <div class="oe_structure"></div>
                        <p style="page-break-before:always;"></p>

                      
                     -->



              
                       
                           
         </div>
         </div>

                    </t>
                     <t t-else="">
                     </t>
                     </t>
                </t>
                
            </t>

                
        </template>


        </odoo>