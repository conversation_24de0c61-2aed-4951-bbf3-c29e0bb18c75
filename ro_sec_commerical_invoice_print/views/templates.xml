<?xml version="1.0" encoding="utf-8"?>
<odoo>
 <record id="paperformat_survey_certification" model="report.paperformat">
            <field name="name">Second Commerical invoice</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">35</field>
            <field name="margin_bottom">25</field>
            <field name="margin_left">10</field>
            <field name="margin_right">10</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">30</field>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">105</field>
        </record>

  

        <template id="invoice_sec_external_layout">
      
        <div t-attf-class="header" t-att-style="report_header_style">

              <style>
                                    .no-border-tbody tbody{border:0px !important;}

                                    .no-border{border:0px !important;}
                                    .no-border tbody td{ 
                                        border-top:0px !important;
                                        border-bottom:0px !important;                                        }
                                    .no-border tbody tr{border:0px !important;}
                                    <!-- .add-border tbody tr{border:1px !important;}
                                    .add-border tbody td{border:1px !important;} -->

                                    .td {
                                            border-top:border:0px !important;
                                            border-bottom:border:0px !important;
                                            border-left: 1px solid black !important;
                                            border-right: 1px solid black !important;
                                          
                                        }

                            </style>
            <div class="row no-border" style="padding-bottom:10px;">
           
               
               
                 <table class="no-border-tbody" style="border:none;margin-top:10px;font-family:'PT Serif',serif;">
                    <td class="no-border col-5" style="border:none;" >
                     <img t-att-src="'/ro_sec_commerical_invoice_print/static/src/img/logoh.png'" alt="Company Logo" style="float: left; margin-right: 10px;"/>
               
                   </td>
                   <td class="no-border col-7" style="text-align:left;border:none;" >

                          
                    <b class="no-border" style="font-size:16; padding-bottom:10px;font-family:'PT Serif',serif;"> Commercial Invoice</b>
                  
                
                 </td>
                 </table>
               
            </div>
            
        </div>
 
        <!-- <div t-attf-class="article o_report_layout_standard o_company_#{o.company_id.id}_layout {{  'o_report_layout_background' if o.company_id.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % o.company_id.layout_background_image.decode('utf-8') if o.company_id.layout_background_image and o.company_id.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if o.company_id.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')"> -->
        <!-- <div t-attf-class="article o_report_layout_standard o_company_#{o.company_id.id}_layout {{ 'o_report_layout_background' if o.company_id.layout_background in ['Geometric', 'Custom'] else '' }}" t-attf-style="background-image: url('/ro_sec_commerical_invoice_print/static/src/img/wm.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">   
         -->
         <div t-attf-class="article o_report_layout_standard " t-attf-style="background-image: url('/ro_sec_commerical_invoice_print/static/src/img/bg.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">   
            
            <div class="pt-5" >
           
                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
        </div>


        <div t-attf-class="footer">
            <div style="margin-top:20px;">
                <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                 </div>  
            <div class="row">
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>

                                    <!-- <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;"> -->
                                    <!-- </a> -->      
                                        <span style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;"><EMAIL> </span>
                                   
                                    
                                    </div>
                                   
                                    
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/world-wide-web.png'" alt="web" style="height:25px;wedth:25px;"/>

                                      <!-- <a t-att-href="'http://www.xeedcorp.com/'" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">
                                                            www.xeedcorp.com
                                     </a> -->
                                     <span style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">  www.xeedcorp.com</span>
                                    </div>
                                   
                                  
                                     <div class="col-4" style="text-align:center;font-family:'PT Serif',serif;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/telephone.png'" alt="phone" style="height:25px;wedth:25px;"/>
                                     
                                     <strong><span style="color:#5a855f;">+2010 915 56 556</span></strong>
                                    </div>
                             </div>
            </div>

       
    </template>

       <record id="action_sec_commerical_invoice_report" model="ir.actions.report">
            <field name="name"> Second Commerical Invoice</field>
            <field name="model">account.move</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_sec_commerical_invoice_print.sec_commerical_report_template</field>
            <field name="report_file">ro_sec_commerical_invoice_print.sec_commerical_report_template</field>
             <field name="paperformat_id" ref="ro_sec_commerical_invoice_print.paperformat_survey_certification"/>
            <field name="print_report_name">'Second Commerical Invoice'</field>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_type">report</field>
        </record>


         <template id="sec_commerical_report_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="ro_sec_commerical_invoice_print.invoice_sec_external_layout">
                        <link rel="preconnect" href="https://fonts.googleapis.com"/>
                            <link rel="preconnect" href="https://fonts.gstatic.com"/>
                            <link href="https://fonts.googleapis.com/css2?family=PT+Serif:ital,wght@0,400;0,700;1,400;1,700" rel="stylesheet"/>
                        <t t-if="o.line_ids[0].sale_line_ids">    
                        <div class="page">
                        
                                    <div class="col-7" style="text-align: left; font-size:13px; ">
                                        <strong><div style="font-family:'PT Serif',serif;">XEED FOR IMPORT AND EXPORT</div></strong>
                                        <div style="font-family:'PT Serif',serif;" >Building No. 37, Walk of Cairo Trade Center,</div>
                                        <div style="font-family:'PT Serif',serif;" >Kilo 38, Cairo-Alexandria Desert Road -Sheikh Zayed,Egypt</div>
    
                                    </div>
                                    <b><hr style="border: 1px solid black;font-weight: bold;margin-bottom:20px;"/></b>   
                                    <!-- </div> -->

                        
                                <div style="width: 100%; display: flex; justify-content: space-between;">
                                    <style>
                                    .no-border-tbody tbody{border:0px !important;}

                                    .no-border{border:0px !important;}
                                    .no-border tbody td{ 
                                        border-top:0px !important;
                                        border-bottom:0px !important;                                        }
                                    .no-border tbody tr{border:0px !important;}
                                

                                    .td {
                                            border-top:border:0px !important;
                                            border-bottom:border:0px !important;
                                            border-left: 1px solid black !important;
                                            border-right: 1px solid black !important;
                                          
                                        }

                            </style>
                                   <div class="row">
                                   
                                    <div class="col-7" style="text-align: left;">
                                    <table class="no-border-tbody" style="border-collapse: collapse;margin-top:10px;font-size:11px;font-family:'PT Serif',serif;">
                
                                            <tr style="border: none;">
                                                <td style="border: none;">
                                                    <span style="text-align:left;">

                                                        <b><u>Bill To: </u> </b>
                                                    </span>

                                                    <div style="width:300px;">
                                                    <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.name"/>
                                                        <br/>
                                                        <!-- <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.street">
                                                            <span>,</span>
                                                        </t> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.street"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.city">
                                                            <span>,</span>
                                                            
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.city"/>
                                                            <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.zip">
                                                                <span>,</span>
                                                            </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.zip"/>
                                                              <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.country_id.name">
                                                                <span>,</span>
                                                              </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_invoice_id.country_id.name"/>
                                                    </b>

                                         </div>    
                 
                                        </td>
                                               
                                            </tr>
                                            <tr style="border: none;">
                                            <td style="border: none;text-align:left;font-family:'PT Serif',serif;">
                                                <span style="text-align:left;width:100px;padding-right:73px;">
                                                    
                                                  <b>  Invoice Date : </b>
                                
                                                </span>
                                                <b><span t-esc="o.invoice_date.strftime('%d-%b-%Y') if o.invoice_date else ''" style=" direction:ltr;"></span></b>
                                            </td>

                                           
                                            </tr>

                                            <tr style="border: none;">
                                                <td style="border: none;text-align:left;font-family:'PT Serif',serif;">
                                                    <span style="text-align:left;width:100px;padding-right:85px;">
                                                          
                                                          <b>Invoice no : </b>
                                                    
                                                    </span>
                                                    <b><span t-field="o.name"></span> </b>
                                                </td>
                                                
                                            </tr>   
                                            <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:76px;">
                                                       <b> Contract No : </b>


                                                        
                                                
                                                        <!-- Due Date :<span t-field="o.invoice_date_due" style=" direction:ltr;"></span>  -->
                                                
                                                </span>
                                                  <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.client_order_ref"  ></span> </b>
                                            </td>
                                          
                                        </tr>
                                        
                                        <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;font-family:'PT Serif',serif;">
                                                <span  style="text-align:left;width:100px;padding-right:49px;">
                                                        <b>Country Of Origin:  </b>
                                                
                                                </span>
                                                <span ><b>Egypt</b></span>
                                            </td>
                                           
                                            </tr>

                                             <tr style="border: none;">
                                                
                                                <td style="border: none;text-align:left;font-family:'PT Serif',serif;">
                                                <span style="text-align:left;width:100px;padding-right:111px;">
                                                     <b>
                                                    Agent:
                                                    </b>
                                                 </span>  
                                                  <span>
                                                    <b>
                                                      <span t-field="o.line_ids[0].sale_line_ids[0].order_id.agent_id"  ></span>
                                                    </b>
                                                </span> 
                                                </td>
                                               
                                               

                                            </tr>
                                            <tr style="border: none;"> 
                                                <td style="border: none;text-align:left;font-family:'PT Serif',serif;">
                                                    <span style="text-align:left;width:100px;padding-right:88px;">
                                                            <b>Port From:</b>                                                    
                                                    </span>
                                                    <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.loading_port" ></span> </b>
                                                </td>
                                             
                                            </tr>

                                            <tr style="border: none;"> 
                                                <td style="border: none;text-align:left;">
                                                    <span style="text-align:left;width:100px;padding-right:75px;">
                                                            <b>  No of Pallets:  </b>
                                                    
                                                    </span>
                                                      <t t-foreach="o.invoice_line_ids" t-as="line">


                                                         <b><span t-esc="len(o.picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"  /></b>

                                                      </t>
                                                        
                                                </td>
                                              
                                            </tr>


                                            <tr style="border: none;"> 
                                                <td style="border: none;text-align:left;">
                                                    <span style="text-align:left;width:100px;padding-right:78px;">
                                                            
                                                    
                                                        <b> PO Number : </b>
                                                    
                                                    </span>
                                                     <b> <span t-field="o.ref"  ></span> </b>
                                                </td>
                                               
                                            </tr>

                                            <tr style="border: none;"> 
                                                <td style="border: none;text-align:left;">
                                                    <span style="text-align:left;width:100px;padding-right:119px;">
                                                        
                                                         <b>ETD:</b>
                                                    </span>
                                                    <b><span t-esc="o.line_ids[0].sale_line_ids[0].order_id.etd_date.strftime('%d-%b-%Y') if o.line_ids[0].sale_line_ids[0].order_id.etd_date else ''"  ></span> </b>
                                                </td>
                                              
                                            </tr>
                                           
                                            <tr style="border: none;"> 
                                                    <td style="border:none;text-align:left;">
                                                    <p>
                                                         <span style="text-align:left;padding-right:63px;display:inline-block;">                     
                                                                        
                                                         <b> Payment Term: </b> 
                                                        </span>
                                                        <t t-if="o.invoice_payment_term_id and o.invoice_payment_term_id.line_ids" >
                                                        
                                                        <b> <span t-field="o.invoice_payment_term_id.line_ids[0].nb_days" style="padding-right:2px;"></span><span t-field="o.invoice_payment_term_id.line_ids[0].delay_type"></span></b>

                                                        </t>
                                                            
                                                      
                                                    
                                                        <!-- <b> <span 
                                                            t-field="o.line_ids[0].sale_line_ids[0].order_id.payment_term_id" 
                                                            style="display:inline-block; max-width:200px; word-wrap:break-word; white-space:normal;">
                                                        </span> </b> -->
                                                    </p>

                                                    </td>
                                                   
                                            </tr> 


                                    </table>
                                    </div>
                                    <div class="col-5" style="text-align: right;">
                                    <table  class="no-border-tbody" style="border-collapse: collapse;margin-top:10px;font-size:11px;font-family:'PT Serif',serif;">
                                        
                                             <tr style="border: none;">
                                                <td style="border: none;width:140px;text-align:left;">
                                                    <span style="text-align:left;">

                                                        <b><u>ship To: </u> </b>
                                                    </span>

                                                     <div style="width:300px;">
                                                    <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.name"/>
                                                        <br/>
                                                        <!-- <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.street">
                                                            <span>,</span>
                                                        </t> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.street"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.city">
                                                            <span>,</span>
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.city"/>
                                                        <!-- <br/> -->
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.zip">
                                                            <span>,</span>
                                                        </t>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.zip"/>
                                                        <t t-if="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.country_id.name">
                                                            <span>,</span>
                                                        </t>
                                                        <!-- <br/> -->
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.partner_shipping_id.country_id.name"/>
                                                    </b>

                                         </div>  
                                                    <!-- <p><b> <span t-field="o.partner_id.name"></span></b></p> -->
                                                    <!-- <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.notify_id"></span></b></p>
                                                    -->
                                                    <!-- <div><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id"></span></b></div>
                                                    <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id.street"></span>,</b></p> -->
                                                    <!-- <p><b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.consignee_id.street2"></span></b></p> -->


                                                </td>
                                            

                                            </tr>
                                            <tr style="border: none;">
                                            <td style="border: none;text-align:left;">
                                                <span style=" text-align:left;width:100px;padding-right:71px;">
                                                   <b> BL Number : </b>
                                                </span>

                                                
                                                    <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.bl_number"  ></span> 
                                                    </b>
                                                
                                            </td>
                                           

                                            </tr>

                                            <tr style="border: none;">
                                                <td style="border:none;text-align:left;">
                                                    <span style="text-align:left;width:100px;padding-right:100px;">
                                                     
                                                   <b>  Vessel: </b> 
                                                         
                                                          
                                                             <!-- Port of Arrivial:  <span t-field="o.line_ids.sale_line_ids.order_id.destination_port" style=" direction:ltr;"></span>  -->
                                                    
                                                    </span>
                                                    <b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.vessel_name"  ></span> </b>

                                                </td>
                                               
                                            </tr>   

                                             <tr style="border: none;">
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:71px;">
                                                  <b> Destination:</b>
                                                
                                                    <!-- Invoice Date :<span t-field="o.invoice_date" style=" direction:ltr;"></span>  -->
                                                
                                                </span>
                                                <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.ro_country"  ></span>  </b>

                                            </td>
                                            
                                            </tr>
                                          
                                        <tr style="border: none;"> 
                                            <td  style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:61px;">
                                                    
                                                  <b>Container No:</b>
                                                      
                                                     
                                                
                                                </span>
                                                <b><span t-field="o.tracking_source"  ></span> </b>

                                            </td>
                                           
                                        </tr>


                                        <tr style="border: none;"> 
                                            <td  style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:95px;">
                                                    
                                                  <b>Port to:</b>
                                                      
                                                     
                                                
                                                </span>
                                                 <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.destination_port" ></span> </b>
                                            </td>
                                          
                                        </tr>

                                         <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:38px;">
                                                       
                                                   <b>Shipping Method : </b>
                                                
                                                </span>
                                                  <b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.shipping_method_id"  ></span> </b>
                                            </td>
                                          
                                        </tr>

                                        <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:40px;">
                                                      <b>Term of Delivery :</b>
                                                </span>
                                                <b> <span t-field="o.invoice_incoterm_id.code" ></span> </b>
                                            </td>
                                           
                                        </tr>
                                        
                                          <tr style="border: none;"> 
                                            <td style="border: none;text-align:left;">
                                                <span style="text-align:left;width:100px;padding-right:107px;">
                                                       
                                                  <b>  ETA : </b>
                                                
                                                </span>
                                                <b><span t-esc="o.line_ids[0].sale_line_ids[0].order_id.eta_date.strftime('%d-%b-%Y') if o.line_ids[0].sale_line_ids[0].order_id.eta_date else '' "></span> </b>
                                            </td>
                                         
                                        </tr>

                                         <tr style="border: none;"> 
                                                    <td style="border: none;text-align:left;">
                                                        <span style="text-align:left;width:100px;padding-right:106px;">
                                                            <b>   GLN : </b> 
                                                                                                                                
                                                        </span>
                                                        <b>
                                                         <span t-field="o.line_ids.sale_line_ids.order_id.gln"  ></span>
                                                        </b>
                                                    </td>

                                                    
                                          </tr>     

                                          <tr style="border: none;"> 
                                                    <td style="border: none;text-align:left;">
                                                        <span style="text-align:left;width:100px;padding-right:92px;">
                                                            <b> SO NO. : </b> 
                                                                                                                                
                                                        </span>
                                                          <b>
                                                        <span t-field="o.line_ids[0].sale_line_ids[0].order_id.name"></span></b>
                                                    </td>

                                                  
                                          </tr>    
                                          <tr style="border: none;"> 
                                                    <td style="border: none;text-align:left;">
                                                        <span style="text-align:left;width:100px;padding-right:63px;">
                                                            <b> Vat Number : </b> 
                                                                                                                                
                                                        </span>
                                                        <b> 
                                                            <span t-field="o.partner_id.vat"></span>  
                                                        </b>
                                                    </td>

                                                  
                                          </tr>                          
                                           
                                              


                                        
                                       
                                       
                                    </table>
                                    </div>
                            </div>
                         </div>

                         <table  style="border: 1px solid black; width: 100%;  direction: ltr; margin-top:20px;font-size:12px; text-align:left;font-family:'PT Serif',serif;">
                        <tbody>
                            <tr>
                            
                            <td style="border: 1px solid #000; padding: 8px; font-size:13px;"><b>Producer:</b>
                                XEED FOR IMPORT AND EXPORT
                            </td>
                            <td style="border: 1px solid #000; padding: 8px;font-size:13px;"><b>Seller:</b>
                                XEED FOR IMPORT AND EXPORT
                            </td>
                            <td style="border: 1px solid #000; padding: 8px;font-size:13px;"><b>Buyer:</b>
                               <span t-field="o.partner_id"></span>
                             </td>
                            


                           
                                
                             
                        </tr>
                        
                        </tbody>
                      </table>
                        
                      


                        <table class="no-border" style="border-collapse: collapse; width: 100%;  direction: ltr;margin-top:20px;font-size:12px;font-family:'PT Serif',serif;font-weight:bold;">
                        <thead>
                            <tr>
                            <!-- <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">Product</th> -->
                            
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;margin-top:10px;color:white;">Item</th>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Variety</th>

                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Size</th>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Packing kg</th>
                            
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">No.of units </th>
                            
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Gross Weight KG</th>

                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Nat Weight KG</th>
                            
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Unit Price</th>
                            <th style="border: 1px solid #000; padding: 8px; background-color:#1C4437;color:white;">Total Price</th>






                           
                                
                             
                        </tr>
                        
                        </thead>

                        <tbody>
                            <!-- <t t-set="counter" t-value="0"/> -->
                            <t t-foreach="o.invoice_line_ids" t-as="line">
                                
                                <t t-set="picking_ids" t-value="line.move_id.line_ids.sale_line_ids.mapped('order_id.picking_ids').ids"/>
                                <t t-set="cargo_lines" t-value="request.env['cargo.lines'].search([('ro_product', '=', line.product_id.id), ('ro_cargo_id', 'in', picking_ids)])"/>
                        
                                <t t-set="unique_sizes" t-value="cargo_lines.mapped('ro_product_size').sorted()"/>
                        
                                <t t-foreach="unique_sizes" t-as="size">
                                <!-- <t t-set="counter" t-value="counter + 1"/> -->

                                    <tr>
                                        <!-- <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <t t-esc="cargo_lines.ro_product.product_tmpl_id.name"/>
                                        </td> -->
                                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <t t-esc="cargo_lines.ro_product.name"/>
                                        </td>
                                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <t t-if="cargo_lines.ro_product.product_template_attribute_value_ids.attribute_id">
                                                <t t-foreach="cargo_lines.ro_product.product_template_attribute_value_ids" t-as="attribute_line">
                                                    <t t-if="attribute_line.attribute_id.ro_variety">
                                                        <t t-esc="attribute_line.name"/>
                                                    </t>
                                                </t>
                                            </t>
                                        </td>
                                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <t t-esc="size.name"/>
                                        </td>
                                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <t t-esc="cargo_lines.ro_product.weight"/>
                                        </td>
                                        
                                        <!-- Calculate and display total quantity for the current size -->
                                        <t t-set="total_quantity" t-value="cargo_lines.filtered(lambda line: line.ro_product_size == size).mapped('ro_quantity')"/>
                                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <b><t t-esc="sum(total_quantity)"/></b>
                                        </td>
                                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <b><t t-esc="int(sum(total_quantity) * cargo_lines.ro_product.ro_gross_weight)"/></b>
                                        </td>
                                        <td style="border: 1px solid #000; padding: 8px; text-align: center;">
                                            <b><t t-esc="int(sum(total_quantity) * cargo_lines.ro_product.weight)"/></b>
                                        </td>
                                        <td style="border: 1px solid #000; padding: 8px; ">
                                            <!-- <span t-esc="o.currency_id.symbol" style="padding-right:20px;text-align: left;"/> -->
                                            <span t-field="line.price_unit" t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align: right;"/>
                                            
                                        </td>
                                        <td style="border: 1px solid #000; padding: 8px;">
                                            <!-- <span t-esc="o.currency_id.symbol" style="padding-right:20px; text-align: left;"/> -->
                                            <b><span t-esc="sum(total_quantity) * line.price_unit" t-options='{"widget": "monetary", "display_currency": o.currency_id}' style=" text-align: right;"/></b>
                                        </td>
                                    </tr>
                                    <!-- <t t-if="counter % 7 == 0">
                                        <tr><div style="page-break-after: always;"></div></tr>
                                    </t> -->
                                </t>
                        
                                <!-- Page break logic for every 4 rows -->
                                <!-- <t t-if="counter % 4 == 0">
                                    <tr><div style="page-break-after: always;"></div></tr>
                                </t> -->
                            </t>
                            
                            <!-- Footer Row for Total Calculation -->
                            <tr style="border: 1px solid #000;border-top:1px  solid #000 !important;border-bottom:1px solid #000 !important;">
                                <b>
                                    <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="5">
                                     TOTAL:
                                    </td>
                                </b>    
                                       
                                    
                                <td style="border: 1px solid #000; padding: 8px; text-align:center;" colspan="1">
                                    <t t-set="total_gross_weight" t-value="0.0"/>
                                    
                                    <t t-foreach="o.invoice_line_ids" t-as="line">
                                        
                                        <t t-set="picking_ids" t-value="line.move_id.line_ids.sale_line_ids.mapped('order_id.picking_ids').ids"/>
                                        <t t-set="cargo_lines" t-value="request.env['cargo.lines'].search([('ro_product', '=', line.product_id.id), ('ro_cargo_id', 'in', picking_ids)])"/>
                                        
                                        <t t-set="unique_sizes" t-value="cargo_lines.mapped('ro_product_size').sorted()"/>
                                        
                                        <t t-foreach="unique_sizes" t-as="size">
                                            <t t-set="size_lines" t-value="cargo_lines.filtered(lambda line: line.ro_product_size == size)"/>
                                            <t t-set="total_quantity" t-value="sum(size_lines.mapped('ro_quantity'))"/>
                                            <t t-set="line_gross_weight" t-value="total_quantity * cargo_lines.ro_product.ro_gross_weight"/>
                                            <t t-set="total_gross_weight" t-value="total_gross_weight + line_gross_weight"/>
                                        </t>
                                    </t>
                                    
                                    <!-- Display the total gross weight for all lines -->
                                    <t t-esc="int(total_gross_weight)"/>
                                </td>
                                
                                
                           
                                  
                                 <td style="border: 1px solid #000; padding: 8px; text-align:center;" colspan="1">
                                        <t t-set="total_net_weight" t-value="0.0"/>
                                        
                                        <t t-foreach="o.invoice_line_ids" t-as="line">
                                            
                                            <t t-set="picking_ids" t-value="line.move_id.line_ids.sale_line_ids.mapped('order_id.picking_ids').ids"/>
                                            <t t-set="cargo_lines" t-value="request.env['cargo.lines'].search([('ro_product', '=', line.product_id.id), ('ro_cargo_id', 'in', picking_ids)])"/>
                                            
                                            <t t-set="unique_sizes" t-value="cargo_lines.mapped('ro_product_size').sorted()"/>
                                            
                                            <!-- Reset total_gross_weight for each line -->
                                            <t t-set="line_total_net_weight" t-value="0.0"/>
                                    
                                            <t t-foreach="unique_sizes" t-as="size">
                                                <t t-set="size_lines" t-value="cargo_lines.filtered(lambda line: line.ro_product_size == size)"/>
                                                <t t-set="total_quantity" t-value="sum(size_lines.mapped('ro_quantity'))"/>
                                                <t t-set="line_net_weight" t-value="total_quantity * cargo_lines.ro_product.weight"/>
                                                <t t-set="line_total_net_weight" t-value="line_total_net_weight + line_net_weight"/>
                                            </t>
                                            
                                            <!-- Add the line_total_gross_weight to the total_gross_weight -->
                                            <t t-set="total_net_weight" t-value="total_net_weight + line_total_net_weight"/>
                                            
                                            <!-- Display line-specific total gross weight -->
                                        </t>
                                        <t t-esc="int(total_net_weight)"/>

                                        
                                        <!-- Display overall total gross weight for all lines -->
                                        <!-- <br/> -->
                                        <!-- <strong>Total Gross Weight:</strong> <t t-esc="total_gross_weight"/> -->
                                    </td>
    
                                    <td style="border: 1px solid #000; padding: 8px;text-align:left;">
                                    </td>
                                    <b>
                                        <td style="border: 1px solid #000; padding: 8px;" colspan="1">
                                            <t t-set="total_amount" t-value="0.0"/>
                                        
                                            <t t-foreach="o.invoice_line_ids" t-as="line">
                                                
                                                <t t-set="picking_ids" t-value="line.move_id.line_ids.sale_line_ids.mapped('order_id.picking_ids').ids"/>
                                                <t t-set="cargo_lines" t-value="request.env['cargo.lines'].search([('ro_product', '=', line.product_id.id), ('ro_cargo_id', 'in', picking_ids)])"/>
                                                
                                                <t t-set="unique_sizes" t-value="cargo_lines.mapped('ro_product_size').sorted()"/>
                                                
                                                <t t-set="total_quantity" t-value="sum(cargo_lines.mapped('ro_quantity'))"/>
                                                
                                                <!-- Calculate the total amount for the current line -->
                                                <t t-set="line_amount" t-value="total_quantity * line.price_unit"/>
                                                
                                                <!-- Add the line amount to the total amount -->
                                                <t t-set="total_amount" t-value="total_amount + line_amount"/>
                                            </t>
                                            
                                            <!-- Display the total amount for all lines -->
                                            <span t-esc="total_amount"  t-options='{"widget": "monetary", "display_currency": o.currency_id}' style="text-align:right;"/>
                                        </td>
                                        
                                     <!-- <td style="border: 1px solid #000; padding: 8px;text-align:center;" colspan="1"><t t-esc="o.currency_id.symbol"/><t t-esc="o.amount_total"/></td> -->
                                    </b>   
    
    
                               
                                </tr>
    
                        </tbody>
                        
                        </table>

                         <!-- <div t-attf-class="footer"> -->
                                <!-- <div class="row"> -->
                          <!-- <div style="page-break-before: always;">      -->
                           
                            <div class="col-6" style="text-align:left;font-family:'PT Serif',serif;padding-bottom:20px;margin-top:10px;">
                                     <strong>Bank Details</strong>
                                  
                                    
                             </div>

                           <!-- </div>   -->
                                   
                                   
                                     <!-- <div class="col-6" style="text-align:right;">
                                       <strong><p>Stamp</p></strong>
                                    </div> -->
                                   
                                                    
                            <!-- </div> -->
                                    <!-- <div class="text-center" style="border-top: 1px solid black; margin-bottom:10px;">
                                    </div> -->
                                                    <div style="text-align: left; font-size:12px;">
                                                    <div><b>Account Name : </b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_account_name" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                                    <div><b>Bank name:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_bank_name" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                                    <div><b>Currency: </b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.currency_id" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                                    <div><b>Account Number:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_account_number" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                                    <div><b>IBAN:</b><span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_iban" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>
                                                    <div><b>Swift</b> <span t-field="o.line_ids[0].sale_line_ids[0].order_id.pricelist_id.ro_swift" style=" direction:ltr;font-family:'PT Serif',serif;"></span> </div>

                                                    
                                                    
                                                    </div>
                                         
                                
<!-- 

                               <div style="margin-top:20px;">
                    <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                     </div>   
                        

   
                            <div class="row">
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_sec_commerical_invoice_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>

                                    <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">
                                            <EMAIL>
                                    </a>
                                    
                                    </div>
                                   
                     
                                     <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_sec_commerical_invoice_print//static/src/img/world-wide-web.png'" alt="web" style="height:25px;wedth:25px;"/>

                                      <a t-att-href="'http://www.xeedcorp.com/'" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">
                                                            www.xeedcorp.com
                                     </a>
                                    </div>
                                   
                                  
                                     <div class="col-4" style="text-align:center;font-family:'PT Serif',serif;">
                                    <img t-att-src="'ro_sec_commerical_invoice_print//static/src/img/telephone.png'" alt="phone" style="height:25px;wedth:25px;"/>
                                     
                                     <strong><span style="color:#5a855f;">+2010 915 56 556</span></strong>
                                    </div>
                             </div> -->
                        </div>
                         </t>
                     <t t-else="">
                     </t>
                     

<!-- 
                        <div class="oe_structure"></div>
                        <p style="page-break-before:always;"></p> -->

                    </t>
                </t>
            </t>
                
        </template>

        </odoo>