from odoo import models, fields, api

class Account<PERSON>ove(models.Model):
    _inherit = 'account.move'
    
    suitable_picking_ids = fields.Many2many(
        'stock.picking',
        compute='_compute_picking_id',)
    
    picking_id = fields.Many2one(
        'stock.picking',
        string='picking',
        domain="[('id', 'in', suitable_picking_ids)]",
    )
    tracking_source = fields.Char(related='picking_id.carrier_tracking_ref', readonly=True, string="Container No")
   
  
    @api.depends('invoice_origin')
    def _compute_picking_id(self):
     
        for move in self:
            if move.invoice_origin:
               
                sale_order = self.env['sale.order'].search([('name', '=', move.invoice_origin)], limit=1)
                if sale_order and sale_order.picking_ids:

                    move.suitable_picking_ids = sale_order.picking_ids
                else:
                    move.suitable_picking_ids = False
            else:
                move.suitable_picking_ids = False








