from odoo import models, fields, api

class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'
    
    ro_total_quantity_same_size = fields.Float(
        string="Total Quantity (Same Size)",
       
        
    )
    
    ro_computed_gross_weight = fields.Float(
        string='Computed Gross Weight',
      
    )
    ro_computed_net_weight = fields.Float(
        string='Computed Net Weight',
       
    )

    # ro_total_quantity_same_size = fields.Float(
    #     string="Total Quantity (Same Size)",
    #     compute="_compute_total_quantity_same_size",
        
    # )
    
    # ro_computed_gross_weight = fields.Float(
    #     string='Computed Gross Weight',
    #     compute='_compute_product_gross_weight',
    #     store=True
    # )
    # ro_computed_net_weight = fields.Float(
    #     string='Computed Net Weight',
    #     compute='_compute_product_net_weight',
    #     store=True
    # )
    
    


    # @api.depends('ro_inv_product_size', 'sale_line_ids.order_id.picking_ids')
    # def _compute_total_quantity_same_size(self):
    #     for line in self:
    #         total_quantity = 0
    #         # Check if `ro_inv_product_size` has a value
    #         if line.ro_inv_product_size:
    #             # Collect all pickings related to the sale order lines in the invoice
    #             picking_ids = line.move_id.line_ids.sale_line_ids.mapped('order_id.picking_ids').ids

    #             # Find cargo lines with the selected size across related pickings
    #             cargo_lines = self.env['cargo.lines'].search([
    #                 ('ro_product_size', '=', line.ro_inv_product_size.id),
    #                 ('ro_cargo_id', 'in', picking_ids)
    #             ])

    #             # Calculate the total quantity of this size
    #             total_quantity = sum(cargo_lines.mapped('ro_quantity'))

    #         line.ro_total_quantity_same_size = total_quantity
            
            
    # @api.depends('ro_total_quantity_same_size', 'product_id.ro_gross_weight')
    # def _compute_product_gross_weight(self):
    #     for line in self:
    #         line.ro_computed_gross_weight = line.ro_total_quantity_same_size * line.product_id.ro_gross_weight

    # @api.depends('ro_total_quantity_same_size', 'product_id.weight')
    # def _compute_product_net_weight(self):
    #     for line in self:
    #         line.ro_computed_net_weight = line.ro_total_quantity_same_size * line.product_id.weight        




