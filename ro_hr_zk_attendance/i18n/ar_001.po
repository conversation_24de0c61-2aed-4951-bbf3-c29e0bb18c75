# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ro_hr_zk_attendance
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-16 18:09+0000\n"
"PO-Revision-Date: 2024-01-16 18:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__active
msgid "Active"
msgstr "نشط"

#. module: ro_hr_zk_attendance
#: model:ir.actions.server,name:ro_hr_zk_attendance.ro_add_action
msgid "Add Attendance"
msgstr "إضافة الحضور"

#. module: ro_hr_zk_attendance
#: model:ir.actions.server,name:ro_hr_zk_attendance.cron_download_data_ir_actions_server
#: model:ir.cron,cron_name:ro_hr_zk_attendance.cron_download_data
msgid "Add Data"
msgstr "إضافة البيانات"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__added
msgid "Added"
msgstr "أضيف"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,help:ro_hr_zk_attendance.field_zk_machine_attendance__address_id
msgid "Address"
msgstr "العنوان"

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/machine_analysis.py:0
#, python-format
msgid "All data must be downloaded first."
msgstr "يجب تنزيل جميع البيانات أولاً."

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.view_zk_machine_search
msgid "Archived"
msgstr "مؤرشفة"

#. module: ro_hr_zk_attendance
#: model:ir.actions.act_window,name:ro_hr_zk_attendance.ro_action_zk_machine_attendance
#: model:ir.model,name:ro_hr_zk_attendance.model_hr_attendance
msgid "Attendance"
msgstr "الحاضرين "

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid "Attendance Records Deleted."
msgstr "تم حذف سجلات الحضور."

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,help:ro_hr_zk_attendance.field_zk_machine_attendance__added
msgid "Attendance has been created"
msgstr "تم إنشاء الحضور"

#. module: ro_hr_zk_attendance
#: model:ir.ui.menu,name:ro_hr_zk_attendance.menu_zk_attendance_view
msgid "Attendance log"
msgstr "سجل الحضور"

#. module: ro_hr_zk_attendance
#: model:ir.actions.act_window,name:ro_hr_zk_attendance.zk_machine_action
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__ro_attendances
msgid "Attendances"
msgstr "الحضور"

#. module: ro_hr_zk_attendance
#: model:ir.actions.act_window,name:ro_hr_zk_attendance.zk_machine_data_action
#: model:ir.ui.menu,name:ro_hr_zk_attendance.zk_machine_data_sub_menu
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.view_zk_machine_data_tree
msgid "Attendances Data"
msgstr "بيانات الحضور"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.view_zk_machine_form
msgid "Biometric Device"
msgstr "جهاز البيومترية"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_hr_attendance__ro_device_id
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_hr_employee__ro_device_id
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__name
msgid "Biometric Device ID"
msgstr "معرف الجهاز البيومتري"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.view_zk_machine_tree
msgid "Biometric Machine"
msgstr "آلة القياسات الحيوية"

#. module: ro_hr_zk_attendance
#: model:ir.ui.menu,name:ro_hr_zk_attendance.zk_machine_menu
msgid "Biometric Manager"
msgstr "مدير القياسات الحيوية"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,help:ro_hr_zk_attendance.field_zk_machine_attendance__name
msgid "Biometric device id"
msgstr "معرف الجهاز البيومتري"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__4
msgid "Card"
msgstr "بطاقة"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__attendance_type
msgid "Type"
msgstr "النوع"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__punch_type__0
msgid "Check In"
msgstr "تسجيل الدخول"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__punch_type__1
msgid "Check Out"
msgstr "الخروج"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__comm_pass
msgid "Comm. Pass"
msgstr "بالاتصالات. "

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__company_id
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__company_id
msgid "Company"
msgstr "الشركة"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__create_uid
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__create_uid
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__create_uid
msgid "Created by"
msgstr "من تأليف"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__create_date
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__create_date
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__create_date
msgid "Created on"
msgstr "تم الإنشاء بتاريخ"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_view_zk_machine_attendance_search
msgid "Current Month"
msgstr "الشهر الحالي"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_inherited_hr_attendance_view_filter
msgid "Date"
msgstr "التاريخ"

#. module: ro_hr_zk_attendance
#: model:ir.ui.menu,name:ro_hr_zk_attendance.zk_machine_sub_menu
msgid "Device Configuration"
msgstr "تكوين الجهاز"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__display_name
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__display_name
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: ro_hr_zk_attendance
#: model:ir.actions.server,name:ro_hr_zk_attendance.ro_cron_download_action
msgid "Download"
msgstr "تحميل"

#. module: ro_hr_zk_attendance
#: model:ir.model,name:ro_hr_zk_attendance.model_hr_employee
msgid "Employee"
msgstr "الموظف"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_inherited_view_attendance_tree
msgid "Employee attendances"
msgstr "حضور الموظفين"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__15
msgid "Face"
msgstr "الوجه"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__1
msgid "Finger"
msgstr "الاصبع"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,help:ro_hr_zk_attendance.field_hr_employee__ro_device_id
msgid "Give the biometric device id"
msgstr "أعط معرف الجهاز البيومتري"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,help:ro_hr_zk_attendance.field_zk_machine_attendance__punching_time
msgid "Give the punching time"
msgstr "أعط وقت اللكم"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_inherited_hr_attendance_view_filter
msgid "Group By"
msgstr "المجموعة حسب"

#. module: ro_hr_zk_attendance
#: model:ir.model,name:ro_hr_zk_attendance.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_inherited_hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_view_zk_machine_attendance_search
msgid "Hr Attendance Search"
msgstr "بحث الحضور ساعة"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__id
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__id
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__id
msgid "ID"
msgstr "معرف"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine____last_update
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance____last_update
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data____last_update
msgid "Last Modified on"
msgstr "آخر تعديل بتاريخ"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__write_uid
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__write_uid
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__write_date
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__write_date
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__write_date
msgid "Last Updated on"
msgstr "آخر تحديث بتاريخ"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__name
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.view_zk_machine_form
msgid "Machine IP"
msgstr "IP الجهاز"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__machine_number
msgid "Machine Number"
msgstr "رقم الآلة"

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_view_zk_machine_attendance_search
msgid "Name"
msgstr "الاسم"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__3
msgid "Password"
msgstr "كلمة المرور"

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid "Please install it with 'pip3 install pyzk'."
msgstr "الرجاء تثبيته باستخدام "pip3 install pyzk"."

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__port_no
msgid "Port No"
msgstr "رقم المنفذ"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__punching_time
msgid "Punching Time"
msgstr "وقت اللكم"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__punch_type
msgid "Punching Type"
msgstr "نوع اللكم"

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid "Pyzk module not Found. Please install it with 'pip3 install pyzk'."
msgstr "لم يتم العثور على وحدة Pyzk. "

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,help:ro_hr_zk_attendance.field_zk_machine_attendance__attendance_type
msgid "Select the attendance type"
msgstr "اختر نوع الحضور"

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/machine_analysis.py:0
#, python-format
msgid "Something Wrong."
msgstr "شيء خاطئ."

#. module: ro_hr_zk_attendance
#: model_terms:ir.ui.view,arch_db:ro_hr_zk_attendance.ro_view_zk_machine_attendance_search
msgid "Today"
msgstr "اليوم"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__2
msgid "Type_2"
msgstr "النوع_2"

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid ""
"Unable to clear Attendance log. Are you sure attendance log is not empty."
msgstr "غير قادر على مسح سجل الحضور. "

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid ""
"Unable to connect to Attendance Device. Please use Test Connection button to"
" verify."
msgstr "غير قادر على الاتصال بجهاز الحضور. "

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid ""
"Unable to connect, please check the parameters and network connections."
msgstr "غير قادر على الاتصال، يرجى التحقق من المعلمات واتصالات الشبكة."

#. module: ro_hr_zk_attendance
#. odoo-python
#: code:addons/ro_hr_zk_attendance/models/zk_machine.py:0
#, python-format
msgid "Unable to get the attendance log, please try again later."
msgstr "تعذر الحصول على سجل الحضور، يرجى المحاولة مرة أخرى لاحقًا."

#. module: ro_hr_zk_attendance
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__punch_type__9
#: model:ir.model.fields.selection,name:ro_hr_zk_attendance.selection__zk_machine_attendance__attendance_type__0
msgid "Other"
msgstr "غير ذلك"

#. module: ro_hr_zk_attendance
#: model:ir.actions.server,name:ro_hr_zk_attendance.ro_update_punch_type_action
msgid "Update Punch Type"
msgstr "تحديث نوع الثقب"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_data__ro_users
msgid "Users"
msgstr "المستخدمون"

#. module: ro_hr_zk_attendance
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine__address_id
#: model:ir.model.fields,field_description:ro_hr_zk_attendance.field_zk_machine_attendance__address_id
msgid "Working Address"
msgstr "عنوان العمل"

#. module: ro_hr_zk_attendance
#: model:ir.model,name:ro_hr_zk_attendance.model_zk_machine
msgid "ZK Machine"
msgstr "آلة ZK"

#. module: ro_hr_zk_attendance
#: model:ir.model,name:ro_hr_zk_attendance.model_zk_machine_data
msgid "ZK Machine Data"
msgstr "بيانات آلة ZK"

#. module: ro_hr_zk_attendance
#: model:ir.model,name:ro_hr_zk_attendance.model_zk_machine_attendance
msgid "zk Machine Attendance"
msgstr "الحضور الآلي zk"

