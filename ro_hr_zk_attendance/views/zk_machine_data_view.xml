<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <record id="view_zk_machine_data_tree" model="ir.ui.view">
        <field name="name">zk.machine.data.tree</field>
        <field name="model">zk.machine.data</field>
        <field name="arch" type="xml">
            <tree string="Attendances Data" create="false" edit="false">
                <field name="create_date" />
                <!-- <field name="ro_users" />
                <field name="ro_attendances" /> -->
                <field name="company_id" groups="base.group_multi_company" />
            </tree>
        </field>
    </record>

    <record id="zk_machine_data_action" model="ir.actions.act_window">
        <field name="name">Attendances Data</field>
        <field name="res_model">zk.machine.data</field>
        <field name="view_mode">tree</field>
    </record>

    <record id="ro_cron_download_action" model="ir.actions.server">
        <field name="name">Download</field>
        <field name="model_id" ref="model_zk_machine_data"/>
        <field name="binding_model_id" ref="model_zk_machine_data"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.cron_download()
        </field>
    </record>

    <menuitem id="zk_machine_data_sub_menu" parent="zk_machine_menu" name="Attendances Data"
        action="zk_machine_data_action" sequence="9" />

    
</odoo>