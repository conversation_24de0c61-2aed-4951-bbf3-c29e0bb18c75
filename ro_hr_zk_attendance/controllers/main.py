# -*- coding: utf-8 -*-

import json

from odoo import http, SUPERUSER_ID
from odoo.http import request


class HRController(http.Controller):

    @http.route('/getmachines/<int:company_id>', type='http', auth="api_key", methods=['GET'], csrf=False)
    def getmachines(self, company_id=1, **kwargs):

        machines = request.env['zk.machine'].search(
            [('company_id', '=', company_id)])

        total_machines = []
        for machine in machines:
            total_machines.append({
                'ip': machine.name,
                'port_no': machine.port_no,
                'machine_number': machine.machine_number,
                'comm_pass': machine.comm_pass
            })

        data = json.dumps({'machines': total_machines},
                          ensure_ascii=False).encode('utf8')

        return data

    @http.route('/setattendances/<int:company_id>', type='json', auth="api_key", methods=['POST'], csrf=False)
    def setattendances(self, company_id=1, **kwargs):
        try:
            if kwargs:
                result = request.env['zk.machine.data'].create_attendance_api(
                    kwargs.get('users', False), kwargs.get('attendances', False), company_id)

                if result:
                    state = {'code': 200, 'status': 'OK'}

                else:
                    state = {
                        'code': 401, 'status': 'Unable to get the attendance log, please try again later.'}

            else:
                state = {'code': 402, 'status': 'Something Wrong'}

        except:
            state = {'code': 400, 'status': 'Bad Request'}

        return state
