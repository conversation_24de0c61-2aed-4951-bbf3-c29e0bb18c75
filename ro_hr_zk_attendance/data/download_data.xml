<?xml version="1.0"?>
<odoo noupdate="1">
	<record forcecreate="True" id="cron_download_data" model="ir.cron">
		<field name="name">Add Data</field>
		<field eval="True" name="active" />
		<field name="user_id" ref="base.user_admin" />
		<field name="interval_number">5</field>
		<field name="interval_type">minutes</field>
		<field name="numbercall">-1</field>
		<field name="model_id" ref="ro_hr_zk_attendance.model_zk_machine_data" />
		<field name="state">code</field>
		<field name="code">model.cron_download()</field>
	</record>

	<!--<record forcecreate="True" id="cron_download_data" model="ir.cron">
		<field name="name">Download Data</field>
		<field eval="False" name="active" />
		<field name="user_id" ref="base.user_root" />
		<field name="interval_number">10</field>
		<field name="interval_type">minutes</field>
		<field name="numbercall">-1</field>
		<field name="model_id" ref="ro_hr_zk_attendance.model_zk_machine" />
		<field name="state">code</field>
		<field name="code">model.cron_download()</field>
	</record>-->
</odoo>
