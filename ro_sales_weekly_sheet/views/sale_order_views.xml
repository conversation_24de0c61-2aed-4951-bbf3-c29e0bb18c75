<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_order_form_excel_fields" model="ir.ui.view">
            <field name="name">sale.order.form.excel.fields</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='payment_term_id']" position="after">
                    <field name="ro_commition_perc" />
                    <field name="ro_freight_rate" />
                    <field name="ro_clearance_value" />
                    <field name="ro_actual_claim" />
                </xpath>
            </field>
        </record>
        
        <record id="action_sale_excel_export" model="ir.actions.server">
            <field name="name">Export Excel Sheet</field>
            <field name="model_id" ref="sale.model_sale_order" />
            <field name="binding_model_id" ref="sale.model_sale_order" />
            <field name="state">code</field>
            <field name="code">
                action = records.action_sale_excel_export()
            </field>
            <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
        </record>

        <record id="view_order_tree_excel_export" model="ir.ui.view">
            <field name="name">sale.order.tree.excel.export</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_quotation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <attribute name="multi_edit">1</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>