# =============================================================================
# controllers/main.py
# =============================================================================
from odoo import http
from odoo.http import request
import base64
import io
try:
    import xlsxwriter
except ImportError:
    xlsxwriter = None

class SaleExcelController(http.Controller):
    
    @http.route('/sale/export_excel/<int:order_id>', type='http', auth='user')
    def export_sale_excel(self, order_id, **kwargs):
        """Export sale order as Excel file via direct URL"""
        order = request.env['sale.order'].browse(order_id)
        if not order.exists():
            return request.not_found()
        
        # Generate Excel file
        result = order.action_export_excel()
        return request.redirect(result['url'])