from odoo import models, fields, api
from odoo.exceptions import ValidationError
import datetime

class PeriodSelectionWizard(models.TransientModel):
    _name = 'period.selection.wizard'
    _description = 'Period Selection Wizard'
    
    sale_order_id = fields.Many2one('sale.order', string='Sale Order')
    ro_start_date = fields.Date(string='Start Date', required=True, default=str(datetime.datetime.now()))
    ro_end_date = fields.Date(string='End Date', required=True, default=str(datetime.datetime.now()))
    ro_period_type = fields.Selection([
        ('weekly', 'Weekly'),
        ('yearly', 'Yearly')
    ], string='Period Type', required=True, default='weekly')
    ro_choosen_year = fields.Selection(
        selection=[(str(year), str(year)) for year in range(2000, 2051)],
        string='Year',
        default=str(datetime.datetime.now().year)
    )

    
    def create_excel_sheet(self): 
        print({"self.sale_order_id": self.sale_order_id})
        if self.ro_start_date > self.ro_end_date:
            raise ValidationError("Start Date cannot be greater than End Date")
        if self.ro_period_type == 'weekly':
            return self.sale_order_id.action_weekly_sale_excel_export(self.ro_start_date, self.ro_end_date)
        elif self.ro_period_type == 'yearly':
            # start and end of ro_choosen_year
            start_date = datetime.datetime.strptime(self.ro_choosen_year + '-01-01', '%Y-%m-%d')
            end_date = datetime.datetime.strptime(self.ro_choosen_year + '-12-31', '%Y-%m-%d')
            return self.sale_order_id.action_yearly_sale_excel_export(start_date, end_date)