from odoo import fields, models, api


class CargoLines(models.Model):
    _name = "cargo.lines"
    _description = "Cargo Lines"

    ro_product_domain = fields.Many2many('product.product', compute='_compute_ro_product_domain', store=True)
    ro_product = fields.Many2one(
        'product.product',
        string='Product',
        domain="[]",
        # domain="[('id', 'in', ro_product_domain)]",
        readonly=False,
        help='This field filters products related to the transfer of this cargo line.'
    )
    lot_id = fields.Many2one('stock.lot', string="Pallet")

    ro_package_domain = fields.Many2many('stock.quant.package', compute='_compute_ro_package_domain', store=True)
    ro_package = fields.Many2one(
        'stock.quant.package',
        string='Pallet',
        domain="[('id', 'in', ro_package_domain)]",
        readonly=False,
        help='This field filters packages related to the transfer of this cargo line.'
    )
    ro_cargo_id = fields.Many2one('stock.picking', string='Cargo')

    ro_prod_date = fields.Date(string='Prod Date',related='ro_cargo_id.ro_prod_date',readonly=False)
## to formated date to l code date
    ro_prod_date_formatted = fields.Char(compute='_compute_ro_prod_date_formatted')

    # @api.depends('ro_prod_date')
    # def _compute_ro_prod_date_formatted(self):
    #     for record in self:
    #         if record.ro_prod_date:
    #             date = fields.Date.from_string(record.ro_prod_date)
    #             week_number = date.isocalendar()[1]
    #             day_number = date.isocalendar()[2]  # 1 = Monday, 7 = Sunday
    #             record.ro_prod_date_formatted = f'L {week_number:02d}_{day_number}'

    @api.depends('ro_prod_date')
    def _compute_ro_prod_date_formatted(self):
        for record in self:
            if record.ro_prod_date:
                date = fields.Date.from_string(record.ro_prod_date)
                week_number = date.isocalendar()[1]
                day_number = date.isocalendar()[2]  # 1 = Monday, 7 = Sunday
                record.ro_prod_date_formatted = f'L {week_number:02d}_{day_number}'
            else:
                record.ro_prod_date_formatted = ''  # Ensure value is assigned


    # ro_pallet_code = fields.Char(string='Pallet Code')
    ro_quantity = fields.Float(string='Quantity')
    ro_product_size = fields.Many2one('product.tag', string='Size')
    ro_euro = fields.Char(string='Euro Pallate')
    
    ro_euro_pallate = fields.Boolean(string='Euro Pallet', default=False)
    
    
    ro_data_logger = fields.Char(string='Data Logger')
    
    
    
    
  
    # total_ro_quantity = fields.Float(string="Total Quantity", compute="_compute_total_ro_quantity", store=True)

    # @api.depends('ro_cargo_id', 'ro_quantity')
    # def _compute_total_ro_quantity(self):
    #     for record in self:
    #         if record.ro_cargo_id:
    #             # Get all cargo lines related to this stock.picking (ro_cargo_id)
    #             cargo_lines = self.env['cargo.lines'].search([('ro_cargo_id', '=', record.ro_cargo_id.id)])
    #             # Sum all ro_quantity values
    #             total_quantity = sum(line.ro_quantity for line in cargo_lines)
    #             record.total_ro_quantity = total_quantity

    #             # Find the related stock.move and update the quantity_done
    #             stock_move = self.env['stock.move'].search([('picking_id', '=', record.ro_cargo_id.id)], limit=1)
    #             if stock_move:
    #                 stock_move.quantity= total_quantity
    #         else:
    #             record.total_ro_quantity = 0

    # @api.onchange('ro_quantity', 'ro_cargo_id')
    # def _onchange_ro_quantity(self):
    #     if self.ro_cargo_id:
    #         # Recompute the total ro_quantity and update stock.move
    #         self._compute_total_ro_quantity()

    @api.depends('ro_cargo_id')
    def _compute_ro_product_domain(self):

        # for line in self:
        #     print('dddddddddddddddddddddddddddddd')
        #     print(line.ro_cargo_id)
        #     if line.ro_cargo_id:
        #         line.ro_product_domain = [(6, 0, line.ro_cargo_id.move_ids.filtered(lambda x: x.state == 'done').mapped('product_id').ids)]
        #     else:
        self.ro_product_domain = False

    @api.depends('ro_product', 'ro_cargo_id')
    def _compute_ro_package_domain(self):
        for line in self:
            if line.ro_cargo_id:
                line.ro_package_domain = line.ro_cargo_id.move_line_ids.filtered(lambda x:x.product_id.id == line.ro_product.id).mapped('package_id').ids

                # line.ro_package_domain = line.ro_cargo_id.move_line_ids.filtered(lambda x:x.product_id.id == line.ro_product.id).mapped('package_id')
                # line.ro_package_domain = [(6, 0, packages.ids)]
            else:
                line.ro_package_domain = False

    @api.onchange('ro_cargo_id')
    def _onchange_ro_cargo_id(self):
        if self.ro_cargo_id:
            self.ro_product = False

