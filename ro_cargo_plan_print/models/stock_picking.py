
from odoo import models, fields, api
# from odoo.exceptions import ValidationError

class StockPickingInherited(models.Model):
    _inherit = 'stock.picking'

    ro_cargo_line_ids = fields.One2many('cargo.lines', 'ro_cargo_id', string='Cargo Lines')
    product_ids = fields.Many2many('product.product', compute="_compute_ro_product_domain")
    ro_prod_date = fields.Date(string='Prod Date')
    ro_lines_number= fields.Integer(string='lines number',)
    
    
    @api.onchange('move_ids','id')
    def _compute_ro_product_domain(self):
        for line in self:
          
            # print(line.ro_cargo_ids)
            # line.product_ids = self.move_ids.filtered(lambda x: x.state == 'done').mapped('product_id').ids
            line.product_ids = self.move_ids.mapped('product_id').ids


            # if line.ro_cargo_id:
            #     line.ro_product_domain = [(6, 0, line.ro_cargo_id.move_ids.filtered(lambda x: x.state == 'done').mapped('product_id').ids)]
            # else:
            #     line.ro_product_domain = False



   
    # @api.constrains('ro_lines_number')
    # def _check_ro_lines_number(self):
    #     for record in self:
    #         if record.ro_lines_number < 0 or not float(record.ro_lines_number).is_integer():
    #             raise ValidationError("The number of lines must be a positive integer. Negative values or decimal numbers are not allowed.")
    
    # @api.onchange('ro_lines_number')
    def get_cargo_lines(self):
        def recu(mo):
            childrens = mo._get_children()
            if not childrens:  
                return mo

            result = mo
            for child in childrens:
                result += recu(child)  
            return result
        

        for rec in self:
            if len(rec.move_ids.mapped('product_id'))>1:
                # raise wizard
                pass
            else:
                product = rec.move_ids.mapped('product_id')[0]

                mo_ids = rec.sale_id.ro_mo_ids

                all_mo_childs= self.env['mrp.production']   
                for parent in mo_ids: 
                    mo_childs = recu(parent)
                    if mo_childs:
                        mo_childs = mo_childs[1:]
                        all_mo_childs |= mo_childs

                lines = []
                for sub in all_mo_childs:
                    lines.append((0, 0, {
                            'ro_product': product.id,  # Copy product from the first line
                            'lot_id': sub.lot_producing_id.id,
                            'ro_prod_date': sub.date_start,
                            'ro_quantity': sub.move_raw_ids[0].product_uom_qty,
                            'ro_product_size': False,
                            'ro_euro_pallate': not sub.product_id.split_checkbox,
                            'ro_data_logger': '',
                        }))

                rec.ro_cargo_line_ids = [(5,0,0)] + lines


        # current_lines = len(self.ro_cargo_line_ids)
        
        # # Ensure ro_lines_number is positive (but this check will also be enforced by the constraint)
        # if self.ro_lines_number > 0:
        #     # Ensure we have at least one line
        #     if current_lines > 0:
        #         first_line = self.ro_cargo_line_ids[0]
                
        #         # If ro_lines_number is greater than current lines, add new lines
        #         if self.ro_lines_number > current_lines:
        #             lines_to_add = self.ro_lines_number - current_lines
        #             lines = []
        #             for i in range(lines_to_add):
        #                 lines.append((0, 0, {
        #                     'ro_product': first_line.ro_product.id,  # Copy product from the first line
        #                     'ro_prod_date': False,
        #                     'ro_quantity': 0,
        #                     'ro_product_size': False,
        #                     'ro_euro_pallate': False,
        #                     'ro_data_logger': '',
        #                 }))
        #             self.ro_cargo_line_ids = [(4, first_line.id)] + lines  # Keep the first line, append new ones
        #         elif self.ro_lines_number < current_lines and self.ro_lines_number !=0:
        #             # Remove lines if `ro_lines_number` is less than current count
        #             self.ro_cargo_line_ids = self.ro_cargo_line_ids[:self.ro_lines_number]    

   
# last old code 
# from odoo import models, fields, api

# class StockPickingInherited(models.Model):
#     _inherit = 'stock.picking'

#     ro_cargo_line_ids = fields.One2many(
#         'cargo.lines', 
#         'ro_cargo_id', 
#         string='Cargo Lines'
#     )
#     product_ids = fields.Many2many(
#         'product.product', 
#         compute="_compute_ro_product_domain", 
#         string="Products"
#     )
#     ro_prod_date = fields.Date(string='Prod Date')
#     ro_lines_number = fields.Integer(
#         string='Lines Number', 
#         compute='_compute_ro_lines_number', 
#         inverse='_set_ro_lines_number', 
#         store=True
#     )

#     @api.depends('ro_cargo_line_ids')
#     def _compute_ro_lines_number(self):
#         """Compute the number of cargo lines."""
#         for record in self:
#             record.ro_lines_number = len(record.ro_cargo_line_ids)

#     def _set_ro_lines_number(self):
#         """
#         Adjust `ro_cargo_line_ids` based on the `ro_lines_number` value.
#         Adds or removes lines to match the `ro_lines_number`.
#         """
#         for record in self:
#             current_lines = len(record.ro_cargo_line_ids)
            
#             if record.ro_lines_number > current_lines:
#                 # Add lines
#                 lines_to_add = record.ro_lines_number - current_lines
#                 first_line = record.ro_cargo_line_ids[:1]
#                 lines = []
#                 for _ in range(lines_to_add):
#                     line_data = {
#                         'ro_product': first_line.ro_product.id if first_line else False,
#                         'ro_prod_date': record.ro_prod_date or False,
#                         'ro_quantity': 0,
#                         'ro_product_size': False,
#                         'ro_euro_pallate': False,
#                         'ro_data_logger': '',
#                     }
#                     lines.append((0, 0, line_data))
#                 record.ro_cargo_line_ids = [(4, line.id) for line in record.ro_cargo_line_ids] + lines
            
#             elif record.ro_lines_number < current_lines:
#                 # Remove lines
#                 lines_to_keep = record.ro_cargo_line_ids[:record.ro_lines_number]
#                 record.ro_cargo_line_ids = [(5, 0, 0)] + [(4, line.id) for line in lines_to_keep]

#     @api.depends('ro_cargo_line_ids.ro_product')
#     def _compute_ro_product_domain(self):
#         """
#         Compute the products associated with the cargo lines.
#         """
#         for record in self:
#             product_ids = record.ro_cargo_line_ids.mapped('ro_product').ids
#             record.product_ids = [(6, 0, product_ids)]




#   old old code ............................
# # from odoo import models, fields, api
# # # from odoo.exceptions import ValidationError

# # class StockPickingInherited(models.Model):
# #     _inherit = 'stock.picking'

# #     ro_cargo_line_ids = fields.One2many('cargo.lines', 'ro_cargo_id', string='Cargo Lines')
# #     product_ids = fields.Many2many('product.product', compute="_compute_ro_product_domain")
# #     ro_prod_date = fields.Date(string='Prod Date')
# #     ro_lines_number= fields.Integer(string='lines number',)
    
    
# #     @api.onchange('move_ids','id')
# #     def _compute_ro_product_domain(self):
# #         for line in self:
          
        
# #             line.product_ids = self.move_ids.mapped('product_id').ids


           


  
# #     @api.onchange('ro_lines_number')
# #     def _onchange_ro_lines_number(self):
# #         current_lines = len(self.ro_cargo_line_ids)
        
# #         # Ensure ro_lines_number is positive (but this check will also be enforced by the constraint)
# #         if self.ro_lines_number > 0:
# #             # Ensure we have at least one line
# #             if current_lines > 0:
# #                 first_line = self.ro_cargo_line_ids[0]
                
# #                 # If ro_lines_number is greater than current lines, add new lines
# #                 if self.ro_lines_number > current_lines:
# #                     lines_to_add = self.ro_lines_number - current_lines
# #                     lines = []
# #                     for i in range(lines_to_add):
# #                         lines.append((0, 0, {
# #                             'ro_product': first_line.ro_product.id,  # Copy product from the first line
# #                             'ro_prod_date': False,
# #                             'ro_quantity': 0,
# #                             'ro_product_size': False,
# #                             'ro_euro_pallate': False,
# #                             'ro_data_logger': '',
# #                         }))
# #                     self.ro_cargo_line_ids = [(4, first_line.id)] + lines  # Keep the first line, append new ones
# #                 elif self.ro_lines_number < current_lines and self.ro_lines_number !=0:
# #                     # Remove lines if `ro_lines_number` is less than current count
# #                     self.ro_cargo_line_ids = self.ro_cargo_line_ids[:self.ro_lines_number]    

   