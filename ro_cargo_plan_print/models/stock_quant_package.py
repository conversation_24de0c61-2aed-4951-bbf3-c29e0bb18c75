from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import datetime

class StockQuantPackage(models.Model):
    _inherit = 'stock.quant.package'

    @api.constrains('name')
    def _check_unique_name_per_year(self):
        current_year = datetime.now().year
        for package in self:
            existing_package = self.search([
                ('name', '=', package.name),
                ('create_date', '>=', f'{current_year}-01-01 00:00:00'),
                ('create_date', '<=', f'{current_year}-12-31 23:59:59'),
                ('id', '!=', package.id),
            ])
            if existing_package:
                raise ValidationError('The package name must be unique within the same year.')

