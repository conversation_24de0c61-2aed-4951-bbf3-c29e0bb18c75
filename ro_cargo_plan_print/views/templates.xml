<?xml version="1.0" encoding="utf-8"?>
<odoo>
<record id="paperformat_cargo" model="report.paperformat">
        <field name="name">cargo paperformat</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">25</field>
        <field name="margin_left">15</field>
        <field name="margin_right">15</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">1</field>
        <field name="disable_shrinking" eval="True"/>
        <field name="dpi">110</field>
    </record>


  

        <template id="cargo_external_layout">
      
        
 
        <!-- <div t-attf-class="article o_report_layout_standard o_company_#{o.company_id.id}_layout {{  'o_report_layout_background' if o.company_id.layout_background in ['Geometric', 'Custom']  else  '' }}" t-attf-style="background-image: url({{ 'data:image/png;base64,%s' % o.company_id.layout_background_image.decode('utf-8') if o.company_id.layout_background_image and o.company_id.layout_background == 'Custom' else '/base/static/img/bg_background_template.jpg' if o.company_id.layout_background == 'Geometric' else ''}});" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')"> -->
        <div t-attf-class="article o_report_layout_standard " t-attf-style="background-image: url('/ro_cargo_plan_print/static/src/img/test.png');" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">   
            <div class="pt-5">
           
                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
        </div>

         <div t-attf-class="footer">
            <div style="margin-top:1px;font-size:12px;">
                <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                 </div>  
            <div class="row" >
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;font-size:12px"/>

                                    <!-- <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;"> -->
                                    <!-- </a> -->      
                                        <span style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;"><EMAIL> </span>
                                   
                                    
                                    </div>
                                   
                                    
                                    <div class="col-4" style="text-align:center;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/world-wide-web.png'" alt="web" style="height:25px;wedth:25px;font-size:12px"/>

                                      <!-- <a t-att-href="'http://www.xeedcorp.com/'" style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;">
                                                            www.xeedcorp.com
                                     </a> -->
                                     <span style="color:#5a855f; text-decoration: underline;font-family:'PT Serif',serif;font-size:12px">www.xeedcorp.com</span>
                                    </div>
                                   
                                  
                                     <div class="col-4" style="text-align:center;font-family:'PT Serif',serif;">
                                    <img t-att-src="'ro_commerical_invoice_print//static/src/img/telephone.png'" alt="phone" style="height:25px;wedth:25px;font-size:12px"/>
                                     
                                     <strong><span style="color:#5a855f;">+2010 915 56 556</span></strong>
                                    </div>
                             </div>
            </div>

        
    </template>

       <record id="action_cargo_report" model="ir.actions.report">
            <field name="name">cargo print</field>
            <field name="model">stock.picking</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_cargo_plan_print.cargo_report_template</field>
            <field name="report_file">ro_cargo_plan_print.cargo_report_template</field>
             <field name="paperformat_id" ref="ro_cargo_plan_print.paperformat_cargo"/>
            <field name="print_report_name">'Cargo print'</field>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_type">report</field>
        </record>


         <template id="cargo_report_template">
            <t t-call="web.html_container">
                <t t-call="ro_cargo_plan_print.cargo_external_layout">
                     <link rel="preconnect" href="https://fonts.googleapis.com"/>
                    <link rel="preconnect" href="https://fonts.gstatic.com"/>
                    <link href="https://fonts.googleapis.com/css2?family=PT+Serif:ital,wght@0,400;0,700;1,400;1,700" rel="stylesheet"/>
                    <t t-foreach="docs" t-as="o">
                    <!-- <t t-if="o.line_ids[0].sale_line_ids"> -->

                        <div class="page">

                        <!-- <table class="text-center" style="border-collapse: collapse; width: 100%;  direction: ltr; margin-top:20px; font-family:'PT Serif',serif;"> -->
                        <table style="border-collapse: collapse; width: 100%;margin-right:10px;font-family:'PT Serif',serif; font-size:11px;font-weight: bold;">
                    <tbody>
                            <tr>
                              <th style="border: 1px solid #000; padding: 8px; background-color:#FFF2CC;margin-top:1px;text-align:center;" colspan="2">CARGO PLAN </th>
                            </tr>
                          <!-- </thead> -->
                        <!-- <thead> -->

                      <t t-foreach="o.move_ids" t-as="mline">
                        <tr style="width:100%;">
                          <td style="border: 1px solid #000; padding: 8px;text-align:left;" colspan="2">
                            <div>
                              <span>Container No: <b><t t-esc="o.carrier_tracking_ref"/></b></span>
                            </div>
                            <div>
                              <span>PO.NO: <b><t t-esc="o.sale_id.client_order_ref"/></b></span>
                            </div>
                            <!-- <t t-foreach="o.ro_cargo_line_ids" t-as="line"> -->
                            <div>
                              <span>NO Of Pallet: <b><t t-esc="len(o.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == mline.product_id.id).mapped('lot_id'))"/></b></span>
                            </div>
                            <!-- </t> -->
                            <div>
                            
                              <span>NO Of Cartons: <b><span t-esc="mline.quantity"/></b></span>
                            </div>
                            <div>
                              <span>Variety:<b><span t-esc="mline.product_id.name"/></b><b><t t-if="mline.product_id.product_template_attribute_value_ids.attribute_id"><span><t t-foreach="mline.product_id.product_template_attribute_value_ids" t-as="attribute_line"><t t-if="attribute_line.attribute_id.ro_variety"><b>(<t t-esc="attribute_line.name"/>)</b></t></t></span></t><t t-else=""><span></span></t></b></span></div>
                            <div><span>Client: <b><t t-esc="o.sale_id.partner_id.name"/></b></span></div>
                          </td>
                        </tr>
                        <!-- <tr>
                          <td style="border: 1px solid #000;">
                          <div class="container">
                              <t t-set="counter" t-value="0"/>
                              <t t-foreach="o.ro_cargo_line_ids" t-as="line">
                              
                                
                                <t t-set="counter" t-value="counter + 1"/>
                                  <t t-if="counter % 2 == 0 or counter == len(o.ro_cargo_line_ids)">
                                  <div class="row">
                                  <div class="col-6" style="padding:5px; text-align:left;">
                                    <div><span>Pord Date:</span></div>
                                    <div><span>Pallet code:</span><span t-esc="line.ro_pallet_code"/></div>
                                    <div><span>size</span><span t-esc="line.ro_product_size.name"/></div>
                                    <div><span>Variety:<b><span t-esc="line.ro_product.name"/></b><b><t t-if="line.ro_product.product_template_attribute_value_ids.attribute_id"><span><t t-foreach="line.ro_product.product_template_attribute_value_ids" t-as="attribute_line"><t t-if="attribute_line.attribute_id.ro_variety"><b>(<t t-esc="attribute_line.name"/>)</b></t></t></span></t><t t-else=""><span></span></t></b></span></div>
                                  </div>
                                    </div>
                                </t> 
                                
                                
                            
                              </t> 

                              
                            </div>

                          </td>
                        </tr> -->
                        <tr>
                          <td style="border: 1px solid #000;text-align:left;font-weight: bold;">
                            <t t-foreach="o.ro_cargo_line_ids" t-as="line">
                              <t t-if="line_index % 2 == 0">
                                  <div style="border-bottom: 1px solid #000;text-align:left;padding-left:8px;padding-bottom:1px;">
                                      <!-- <div><span>Pord Date:</span><span t-field="line.ro_prod_date" t-options='{"format": "dd-MM-yyyy"}'/></div> -->
                                      <div><span>Prod Date:</span><span t-field="line.ro_prod_date_formatted"/></div>

                                    <div><span>Pallet code:</span><span t-esc="line.lot_id.name"/>
                                    <t t-if="line.ro_data_logger">
                                            <b><span style="padding-left:10px; font-size:11px;color:#FF0000;">Data Logger</span></b>
                                          <b style="color:#FF0000;"> ( <span style="font-size:11px;color:#FF0000;" t-esc="line.ro_data_logger"/>)</b>
                                    </t>
                                    <!-- <div> -->
                                    <!-- <t t-if="line.ro_euro"> -->
                                    <t t-if="line.ro_euro_pallate">

                                            <b><span style="padding-left:5px; font-size:11px;color:#FF0000;">Euro Pallet</span></b>
                                          <!-- <b style="color:#FF0000;"> ( <span style="font-size:1;color:#FF0000;" t-esc="line.ro_euro"/>)</b> -->
                                    </t>
                                    <!-- </div> -->
                                  </div>
                                  
                                  <div>
                                    <span>size :</span><span t-esc="line.ro_product_size.name"/> 
                                    <span style="padding-left:35px; font-size:11px;">Count of Cartons:</span><span t-esc="line.ro_quantity"/>
                                     <!-- old count of cartons codee -->
                                    <!-- <t t-if="len(line.ro_package.quant_ids) != 0">
                                      <span style="padding-left:120px;">Count of Cartons:</span><span t-esc="line.ro_package.quant_ids[0].quantity"/>
                                    </t>   -->
                                  </div>
                                  <div style="padding-bottom:2px;">
                                    <span >Variety:<span t-esc="line.ro_product.name"/><t t-if="line.ro_product.product_template_attribute_value_ids.attribute_id"><span><t t-foreach="line.ro_product.product_template_attribute_value_ids" t-as="attribute_line"><t t-if="attribute_line.attribute_id.ro_variety">(<t t-esc="attribute_line.name"/>)</t></t></span></t><t t-else=""><span></span></t></span></div>
                                 
                                  
                                  </div>
                                
                              </t>
                            </t>
                          </td>

                          <td style="border: 1px solid #000;text-align:left;font-weight: bold;">
                            <t t-foreach="o.ro_cargo_line_ids" t-as="line">
                                <t t-if="line_index % 2 != 0">
                                    <div style="border-bottom: 1px solid #000;text-align:left;padding-left:8px;padding-bottom:1px;">
                                      <!-- <div><span>Pord Date:</span><span t-field="line.ro_prod_date" t-options='{"format": "dd-MM-yyyy"}' /></div> -->
                                      <div><span>Prod Date:</span><span t-field="line.ro_prod_date_formatted"/></div>

                                      <div><span>Pallet code:</span><span t-esc="line.lot_id.name"/>
                                      <t t-if="line.ro_data_logger">
                                              <b><span style="padding-left:15px; font-size:11px;color:#FF0000;">Data Logger</span></b>
                                            <b style="color:#FF0000;"> ( <span style="font-size:11px;color:#FF0000;" t-esc="line.ro_data_logger"/>)</b>
                                      </t>

                                      <!-- <t t-if="line.ro_euro"> -->
                                      <t t-if="line.ro_euro_pallate">

                                      
                                            <b><span style="padding-left:5px; font-size:11px;color:#FF0000;">Euro Pallet</span></b>
                                            <!-- <b style="color:#FF0000;"> ( <span style="font-size:12;color:#FF0000;" t-esc="line.ro_euro"/>)</b> -->
                                      </t>
                                      </div>
                                      

                                      <div><span>size:</span><span t-esc="line.ro_product_size.name"/>
                                      <span style="padding-left:40px; font-size:11px;">Count of Cartons:</span><span t-esc="line.ro_quantity"/>
                                      <!-- <t t-if="len(line.ro_package.quant_ids) != 0">
                                        <span style="padding-left:120px;">Count of Cartons:</span><span t-esc="line.ro_package.quant_ids[0].quantity"/>
                                      </t>      -->
                                      </div>
                                      <div><span>Variety:<span t-esc="line.ro_product.name"/><t t-if="line.ro_product.product_template_attribute_value_ids.attribute_id"><span><t t-foreach="line.ro_product.product_template_attribute_value_ids" t-as="attribute_line"><t t-if="attribute_line.attribute_id.ro_variety">(<t t-esc="attribute_line.name"/>)</t></t></span></t><t t-else=""><span></span></t></span></div>
                                      
                                    </div>
                                    
                                </t>
                            </t>
                          </td>

                        </tr>
                      </t>

                      <tr>
                        <td style="border: 1px solid #000;text-align:center;" colspan="2"><b> DOOR</b></td>
                      </tr>
                    </tbody>     

                  </table>


                     
                               
                   
                                    

                                   

                      <br/>
                   

                    <!-- <div style="margin-top:1px; font-size:12px;" >
                    <b> <p style="text-align:center; font-family:'PT Serif',serif;color:#2260A7;">Egyptian Tax Registration Number: ***********</p></b>
                        
                    
                        

                    
                         <div class="row">
                                    <div class="col-4" style="text-align:center;" >
                                    <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color:#5a855f; text-decoration: underline;">
                                            <EMAIL>
                                    </a>
                                    
                                    
                                    </div>
                                   
                                
                                     <div class="col-4" style="text-align:center; font-family:'PT Serif',serif;">
                                      <a t-att-href="'http://www.xeedcorp.com/'" style="color: #5a855f; text-decoration: underline;">
                                                            www.xeedcorp.com
                                     </a>
                                  
                                    </div>
                                   
                                   
                                     <div class="col-4" style="text-align:center;">
                                     <strong><p style="color:#5a855f;;font-family:'PT Serif',serif;">+2010 915 56 556</p></strong>
                                    </div>
                          </div>
            </div> -->
        <!-- </div> -->


                        <!-- <div class="oe_structure"></div> -->
                        <!-- <p style="page-break-before:always;"></p> -->

                      
                    



              
                       
                           
         </div>

                    <!-- </t>
                     <t t-else="">
                     </t> -->
                     </t>
                </t>
                
            </t>

                
        </template>


        </odoo>