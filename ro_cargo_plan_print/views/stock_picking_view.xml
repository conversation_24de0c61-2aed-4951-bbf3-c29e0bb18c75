<odoo>

    <record id="view_cargo_form" model="ir.ui.view">
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
        <xpath expr="//field[@name='owner_id']" position="after">
            <field name="product_ids" widget="many2many_tags" invisible="True"/>
        </xpath>


        <field name="state" position="before">
            <button name="get_cargo_lines" invisible="not state =='done'" string="Get Cargo" type="object" class="oe_highlight" data-hotkey="g"/>
        </field>

        <xpath expr="//field[@name='origin']" position="after">
            
            <field name="ro_prod_date" />
            <!-- <field name="ro_lines_number" /> -->


        </xpath>
            <xpath expr="//page[@name='note']" position="after">
               <page name="cargo_lines" string="Cargo">
                    <field name="ro_cargo_line_ids" widget="one2many">
                        <tree string="lines" editable="bottom">
                            <!-- <field name="ro_product" domain="[('id', 'in', parent.move_lines.product_id.ids)]"/> -->
                             <!-- <field name="ro_product" domain="[('id', 'in',ro_cargo_id.move_ids.product_id.ids)]"/> -->
                            <field name="ro_product_domain" column_invisible="True"/>  

                             <field name="ro_product" domain="[('id', 'in', parent.product_ids)]"/>
                            <field name="ro_package_domain" column_invisible="True"/>    


                             
                                     


                             <!-- <field name="ro_product" domain="[('id', 'in',.product_id.ids)]"/>/ -->

                             
                             
                             <!-- <field name="ro_product"/> -->
                             <!-- <field name="move_ids" invisible="1"/> -->


                            <field name="lot_id"/>  
                            <!-- <field name="move_ids" invisible="1"/>  
                                                       -->
                            <field name="ro_cargo_id" column_invisible="True"/>

                            <field name="ro_prod_date" />
                            <field name="ro_prod_date_formatted" column_invisible="True"  />

                            <!-- <field name="ro_pallet_code"/> -->
                            <field name="ro_quantity"/>
                            <field name="ro_product_size"/>
                            <field name="ro_euro" column_invisible="True" />
                            <field name="ro_euro_pallate"/>
                            <field name="ro_data_logger"/>
                            <!-- <field name="total_ro_quantity" column_invisible="True"/> -->



                            

                            
                     
                                
                        </tree>
                    </field>      
                </page>
            </xpath>
        </field>
    </record>

   <record id="view_tracking_reference_form" model="ir.ui.view">
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock_delivery.view_picking_withcarrier_out_form"/>
        <field name="arch" type="xml">
        
         <xpath expr="//field[@name='carrier_tracking_ref']" position="attributes">
                    
                <attribute name="string">Container Number</attribute>

         </xpath>
          
        </field>
    </record>

</odoo>
