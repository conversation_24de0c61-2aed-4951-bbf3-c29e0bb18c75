# -*- coding: utf-8 -*-
{
    'name': "cargo plan print",

    'summary': "cargo print",

    'description': """
            cargo plan print
    """,

    'author': "Roaya",
    'website': "https://www.roayadm.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'stock',
    'version': '17.0',

    # any module necessary for this one to work correctly
    'depends': ['base','stock','sale','account','stock_delivery'],

    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/stock_picking_view.xml',
        'views/templates.xml',
    ],
    'installable': True,
    'license': 'OPL-1',
    
}

