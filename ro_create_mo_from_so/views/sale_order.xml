<odoo>
    <record id="view_sale_order_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.inherited</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_quotation_send']" position="before">
                <button name="ro_action_create_mo" string="Create MO" type="object" 
                 class="btn-primary"
                 
                />
              </xpath>
              <xpath expr="//div[@name='button_box']" position="inside">
                <button type="object"
                    name="ro_action_view_manufacture_orders"
                    class="oe_stat_button"
                    icon="fa-pencil-square-o"
                    invisible= "ro_mo_order_count == 0"
                    >
                    <field name="ro_mo_order_count" widget="statinfo" string="Manufacture Orders"/>
                </button>
            </xpath>

            <xpath expr="//notebook//page[@name='order_lines']//tree//field[@name='product_uom_qty']" position="attributes">
                <attribute name="string">Actual Quantity</attribute>
            </xpath>
                    
        </field>
    </record>


    <record id="view_sale_order_mo_tree_inherit" model="ir.ui.view">
        <field name="name">sale.order.mo.inherited</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.sale_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invoice_status']" position="after">
                <field name="mo_status" />
            </xpath>
        </field>
    </record>
    
</odoo>
