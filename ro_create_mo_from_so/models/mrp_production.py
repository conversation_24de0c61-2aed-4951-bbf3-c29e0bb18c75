from odoo import models, fields, api, _
from collections import defaultdict



class MrpProduction(models.Model):
    _inherit = 'mrp.production'
   

    ro_so_id = fields.Many2one('sale.order')
    
    ro_mo_order_count = fields.Integer('ro_so_id.ro_mo_order_count')
    
    
    
    def ro_action_preview_sale_order(self):
        return {
            'name': _('Sale Order'),
            'view_mode': 'form',
            'view_id': self.env.ref('sale.view_order_form').id,
            'res_model': 'sale.order',
            'type': 'ir.actions.act_window',
            'res_id': self.ro_so_id.id,
        }
