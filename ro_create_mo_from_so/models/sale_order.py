from odoo import models, fields, api, _
from collections import defaultdict



class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    ro_mo_ids = fields.One2many('mrp.production','ro_so_id', copy=False)
    ro_mo_order_count = fields.Integer(string='Manufacture Order Count', compute='_compute_mo_order_counts')
    
    mo_status = fields.Char(compute="_compute_mo_order_counts")

    
    api.depends('ro_mo_ids')
    def _compute_mo_order_counts(self):
        for record in self:
            record.mo_status = ''
            if len(record.ro_mo_ids)>0:
                record.mo_status = ', '.join(record.ro_mo_ids.mapped(lambda x:dict(x._fields['state'].selection).get(x.state)))
            record.ro_mo_order_count = len(record.ro_mo_ids)
        
    

    def ro_action_create_mo(self):
        for order_line in self.order_line:
            if order_line.product_id.bom_ids:
                existing_manufacture_order =False
                if self.ro_mo_ids:
                    existing_manufacture_order = self.env['mrp.production'].search([
                            ('product_id', '=', order_line.product_id.id),
                            ('ro_so_id', '=', self.id),
                            ('state', 'in', ['draft']),
                        ], limit=1)
                if not existing_manufacture_order:
                    manufacturing_order_vals = {
                        'origin':self.name,
                        'ro_so_id':self.id,         
                        'state':  'draft'   ,
                        'product_id': order_line.product_id.id,
                        'product_qty': order_line.product_uom_qty,
                        'mo_product_tag_ids':order_line.sale_product_tag_ids.ids,
                            }
                                
                    self.env['mrp.production'].create(manufacturing_order_vals)

        return self.ro_action_view_manufacture_orders()
       
                        
                        
                        
    def ro_action_view_manufacture_orders(self):
        self.ensure_one()
        mo_orders = self.ro_mo_ids   
        result = self.env['ir.actions.act_window']._for_xml_id('mrp.mrp_production_action')
        if len(mo_orders) > 1:
            result['domain'] = [('id', 'in', mo_orders.ids)]
        elif len(mo_orders) == 1:
            result['views'] = [(self.env.ref('mrp.mrp_production_form_view', False).id, 'form')]
            result['res_id'] = mo_orders.id
        else:
            result = {'type': 'ir.actions.act_window_close'}
        return result
    
    
      
        
        