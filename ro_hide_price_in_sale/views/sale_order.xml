<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Total in sale tree -->
    <record id="view_sale_order_tree_inherited" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.sale_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='amount_total']" position="attributes">
                <attribute name="groups">ro_hide_price_in_sale.ro_hide_total_in_sale</attribute>
            </xpath>
        </field>
    </record>
    
    <!-- unit price in SO -->
    <record id="hide_unit_price_inherited" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="after">
                <field name="price_unit" column_invisible="1" groups="!ro_hide_price_in_sale.ro_hide_total_in_sale"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="attributes">
                <attribute name="groups">ro_hide_price_in_sale.ro_hide_total_in_sale</attribute>
            </xpath>
        </field>
    </record> 


    <!-- hide tax_excluded -->
    <record id="hide_tax_excluded_in_so_view_inherited_view" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/tree/field[@name='price_subtotal']" position="attributes">
                <attribute name="groups">ro_hide_price_in_sale.ro_hide_total_in_sale</attribute>
            </xpath>
        </field>
    </record>

       <!-- hide tax_included -->
       <record id="hide_tax_included_in_so_form_view_inherited" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
                <xpath expr="//field[@name='order_line']/tree/field[@name='price_total']" position="attributes">
                    <attribute name="groups">ro_hide_price_in_sale.ro_hide_total_in_sale</attribute>
                </xpath>
        </field>
    </record>

    <!-- //hide total and untaxed amount -->
    <record id="hide_total_and_untaxed_amount_in_so_form_view_inherited" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
                <xpath expr="//group[@name='sale_total']" position="attributes">
                    <attribute name="groups">ro_hide_price_in_sale.ro_hide_total_in_sale</attribute>
                </xpath>
        </field>
    </record>


</odoo>
