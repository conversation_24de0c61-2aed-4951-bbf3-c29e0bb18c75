from odoo.addons.product.models.product_product import ProductProduct

def get_product_multiline_description_sale(self):
    """ Compute a multiline description of this product, in the context of sales.
        It will often be used as the default description of a sale order line referencing this product.
    """
    name = self.name
    if self.description_sale:
        name += '\n' + self.description_sale

    attribute_descriptions = []
    for attribute_value in self.product_template_attribute_value_ids:
        if not attribute_value.attribute_id.ro_visible_sol:
            attribute_descriptions.append(attribute_value.name)

    if attribute_descriptions:
        name += '\n' + '\n'.join(attribute_descriptions)

    return name
ProductProduct.get_product_multiline_description_sale = get_product_multiline_description_sale
