from odoo import models, fields, api

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    attr_1 = fields.Char(string='Attr 1', compute='_compute_attribute_values')
    attr_2 = fields.Char(string='Attr 2', compute='_compute_attribute_values')
    attr_3 = fields.Char(string='Attr 3', compute='_compute_attribute_values')
    attr_4 = fields.Char(string='Attr 4', compute='_compute_attribute_values')
    

    @api.depends('product_id', 'product_id.product_template_attribute_value_ids')
    def _compute_attribute_values(self):
        for line in self:
            line.attr_1 = ''
            line.attr_2 = ''
            line.attr_3 = ''
            line.attr_4 = ''
            

            if line.product_id:
                attribute_values = line.product_id.product_template_attribute_value_ids
                attributes = [attr for attr in attribute_values if attr.attribute_id.ro_visible_sol]

                if len(attributes) > 0:
                    line.attr_1 = attributes[0].name
                if len(attributes) > 1:
                    line.attr_2 = attributes[1].name
                if len(attributes) > 2:
                    line.attr_3 = attributes[2].name
                if len(attributes) > 3:
                    line.attr_4 = attributes[3].name    

    
