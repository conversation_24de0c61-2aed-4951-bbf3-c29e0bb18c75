# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

class stockQuant(models.Model):

    _inherit = 'stock.quant'


    @api.constrains('quantity','company_id')
    def _check_quantity(self):
        for quant in self:
            quantity = quant.quantity
            location_id = quant.location_id

            if quantity < 0  and location_id.usage == 'internal':
                raise ValidationError(_('Not enough quantity To Proceed in {} for {}.'.format(quant.product_id.name, quant.location_id.name)))