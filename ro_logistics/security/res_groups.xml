<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.module.category" id="ro_logistics.module_category_logistics">
        <field name="name">Logistics</field>
    </record>

    <record id="group_logistics_user" model="res.groups">
        <field name="name">Logistics User</field>
        <field name="category_id" ref="ro_logistics.module_category_logistics"/>
    </record>

    <!-- <record id="group_logistics_manager" model="res.groups">
        <field name="name">Logistics Manager</field>
        <field name="category_id" ref="ro_logistics.module_category_logistics"/>
        <field name="implied_ids" eval="[(4, ref('ro_logistics.group_logistics_user'))]"/>
    </record> -->

</odoo>
