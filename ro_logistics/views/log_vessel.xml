<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Logistics Vessel Config -->
        <record id="log_vessel_config_form_view" model="ir.ui.view">
            <field name="name">Logistics Vessel config.form</field>
            <field name="model">log.vessel</field>
            <field name="arch" type="xml">
                <form string="Logistics Vessel">
                    <sheet>
                        <group>
                            <group>
                                <field name="name" />
                            </group>
                            <group>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>


        <record id="log_vessel_config_tree_view" model="ir.ui.view">
            <field name="name">Logistics Vessel config.tree</field>
            <field name="model">log.vessel</field>
            <field name="arch" type="xml">
                <tree string="Logistics Vessel" editable="bottom">
                    <field name="name" />
                </tree>
            </field>
        </record>

        <!-- actions opening model views on models -->
        <record id="log_vessel_config_action_window" model="ir.actions.act_window">
            <field name="name">Logistics Vessel</field>
            <field name="res_model">log.vessel</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree</field>
        </record>    
        <menuitem action="log_vessel_config_action_window" id="log_vessel_config" sequence="66" parent="sale.menu_sale_config"/>
  

    </data>
</odoo>
