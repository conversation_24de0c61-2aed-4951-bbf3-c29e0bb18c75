<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Ports Config -->
        <record id="ports_config_form_view" model="ir.ui.view">
            <field name="name">Ports config.form</field>
            <field name="model">res.port</field>
            <field name="arch" type="xml">
                <form string="Ports">
                    <sheet>
                        <group>
                            <group>
                                <field name="name" />
                            </group>
                            <group>
                                <field name="port_code" />
                                <field name="country_id" />
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>


        <record id="ports_config_tree_view" model="ir.ui.view">
            <field name="name">Ports config.tree</field>
            <field name="model">res.port</field>
            <field name="arch" type="xml">
                <tree string="Ports" editable="bottom">
                    <field name="name" />
                    <field name="country_id" />
                    <field name="port_code" />
                </tree>
            </field>
        </record>

        <!-- actions opening model views on models -->
        <record id="port_config_action_window" model="ir.actions.act_window">
            <field name="name">Ports</field>
            <field name="res_model">res.port</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree</field>
        </record>    
        <menuitem action="port_config_action_window" id="ports_config" sequence="65" parent="sale.menu_sale_config"/>
  

    </data>
</odoo>
