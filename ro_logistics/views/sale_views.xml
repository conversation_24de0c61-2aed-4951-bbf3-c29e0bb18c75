<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
  <data>

    <record id="sale_order_logistics_fields_search_view" model="ir.ui.view">
      <field name="name">sale.order.logistics_search</field>
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale.view_sales_order_filter"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='partner_id']" position="after">
          <field name="gln" />
        </xpath>
      </field>
    </record>

    <record id="sale_order_logistics_fields_form_view" model="ir.ui.view">
        <field name="name">sale.order.logistics_fields.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
          
          <xpath expr="//div[@name='button_box']" position="inside">

            <button class="oe_stat_button" name="action_view_po_logistics" type="object" icon="fa-plane" invisible="po_logistics_count == 0" >
                <div class="o_field_widget o_stat_info">
                    <span class="o_stat_value"><field name="po_logistics_count"/></span>
                    <span class="o_stat_text">Logistics Order</span>
                </div>
            </button>

          </xpath>


          <xpath expr="//page[@name='other_information']" position="after">
            <page name="logistics" string="Logistics">
              <group>
                <group>
                  <field name="cut_off_date" />
                  <field name="loading_date" />
                  <field name="etd_date" />
                  <field name="eta_date" />
                  <field name="ata_date" />
                </group>
                <group>
                  <field name="booking_number" />
                  <field name="bl_number" />
                  <field name="vessel_name" />
                  <field name="gln" />      

                  <field name="loading_port" />
                  <field name="destination_port" />
                  <field name="shipping_method_id"/>
                  <field name="shipping_status_id"/>

                  <field name="agent_id" />
                </group>
              </group>
            </page>

            <page name="logistics_purchase" string="Purchase Logistics">
              <button name="create_logistic_purchase" string="Create Purchase" type="object" class="oe_highlight"/>

              <field name="logistic_purchase_ids" >
                <tree editable="bottom">
                  <field name="name" column_invisible="1"/>
                  <field name="product_uom_category_id" column_invisible="1"/>

                  <field name="product_template_id" string="Product"/>
                  <field name="product_id" />
                
                  <field name="vendor_id" />
                  
                  <field name="analytic_distribution" widget="analytic_distribution" options="{'force_applicability': 'optional', 'disable_save': true}"/>

                  <field name="quantity" />
                  <field name="product_uom" />
                  <field name="unit_price" />
                  <field name="currency_id" required="1"/>

                  <field name="price_subtotal" />

                  <field name="po_created" readonly="1" force_save="1"/>
                </tree>
              </field>

            </page>

          </xpath>

          <xpath expr="//group[@name='order_details']" position="inside">
            <field name="incoterm" options="{'no_open': True, 'no_create': True}"/>
            <field name="consignee_id" />
            <field name="notify_id" />
            <field name="sec_notify_id" />
          </xpath>

        </field>
    </record>

    <record id="sale_order_logistics_sale_stock_form_view" model="ir.ui.view">
      <field name="name">sale.order.logistics_sale_stock.form</field>
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale_stock.view_order_form_inherit_sale_stock"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='incoterm']" position="attributes">
          <attribute name="invisible" >1</attribute>
        </xpath>
      </field>
    </record>

    <record id="sale_order_logistics_fields_tree" model="ir.ui.view">
      <field name="name">sale.order.logistics_fields.tree</field>
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale.sale_order_tree"/>
      <field name="arch" type="xml">
        
        <xpath expr="//field[@name='validity_date']" position="after">
          <field name="cut_off_date" optional="hide" />
          <field name="loading_date" optional="hide" />
          <field name="etd_date" optional="hide" />
          <field name="eta_date" optional="hide" />
          <field name="ata_date" optional="hide" />

          <field name="booking_number" optional="hide" />
          <field name="bl_number" optional="hide" />
          <field name="vessel_name" optional="hide" />
          <field name="gln" optional="hide" />

          <field name="loading_port" optional="hide" />
          <field name="destination_port" optional="hide" />
          <field name="shipping_method_id" optional="hide"/>
          <field name="shipping_status_id" optional="hide"/>

          <field name="agent_id" optional="hide" />
          <field name="consignee_id" optional="hide" />
          <field name="notify_id" optional="hide" />
          <field name="sec_notify_id" optional="hide" />
        </xpath>

      </field>
    </record>
      

    <!-- Sale Orders -->
    <record id="action_sale_logistics_orders" model="ir.actions.act_window">
      <field name="name">Sales Orders</field>
      <field name="res_model">sale.order</field>
      <field name="view_mode">tree,kanban,form,calendar,pivot,graph,activity</field>
      <field name="search_view_id" ref="sale.sale_order_view_search_inherit_sale"/>
      <field name="context">{}</field>
      <field name="domain">[('state', 'not in', ('draft', 'sent', 'cancel'))]</field>
      <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
              Create a new quotation, the first step of a new sale!
          </p><p>
              Once the quotation is confirmed, it becomes a sales order.<br/> You will be able to create an invoice and collect the payment.
          </p>
      </field>
    </record>

    <record id="sale_order_action_view_logistics_order_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="sale.view_order_tree"/>
        <field name="act_window_id" ref="action_sale_logistics_orders"/>
    </record>

    <record id="sale_order_action_view_logistics_order_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="sale.view_sale_order_kanban"/>
        <field name="act_window_id" ref="action_sale_logistics_orders"/>
    </record>

    <record id="sale_order_action_view_logistics_order_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="3"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="sale.view_order_form"/>
        <field name="act_window_id" ref="action_sale_logistics_orders"/>
    </record>

    <record id="sale_order_action_view_logistics_order_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="4"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="sale.view_sale_order_calendar"/>
        <field name="act_window_id" ref="action_sale_logistics_orders"/>
    </record>

    <record id="sale_order_action_view_logistics_order_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="sale.view_sale_order_pivot"/>
        <field name="act_window_id" ref="action_sale_logistics_orders"/>
    </record>

    <record id="sale_order_action_view_logistics_order_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="6"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="sale.view_sale_order_graph"/>
        <field name="act_window_id" ref="action_sale_logistics_orders"/>
    </record>

    <!-- Sale Quotation -->
    <record id="action_logistics_sale_quotations" model="ir.actions.act_window">
      <field name="name">Quotations</field>
      <field name="res_model">sale.order</field>
      <field name="view_mode">tree,kanban,form,calendar,pivot,graph,activity</field>
      <field name="search_view_id" ref="sale.sale_order_view_search_inherit_quotation"/>
      <!-- <field name="context">{'search_default_my_quotation': 1}</field> -->
      <field name="domain">[('state', 'in', ('draft', 'sent', 'cancel'))]</field>
      <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
          Create a new quotation, the first step of a new sale!
          </p><p>
          Once the quotation is confirmed by the customer, it becomes a sales order.<br/> You will be able to create an invoice and collect the payment.
          </p>
      </field>
    </record>

    <record id="sale_order_action_view_logistics_quotation_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="sale.view_quotation_tree"/>
        <field name="act_window_id" ref="action_logistics_sale_quotations"/>
    </record>

    <record id="sale_order_action_view_logistics_quotation_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="sale.view_sale_order_kanban"/>
        <field name="act_window_id" ref="action_logistics_sale_quotations"/>
    </record>

    <record id="sale_order_action_view_logistics_quotation_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="3"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="sale.view_order_form"/>
        <field name="act_window_id" ref="action_logistics_sale_quotations"/>
    </record>

    <record id="sale_order_action_view_logistics_quotation_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="4"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="sale.view_sale_order_calendar"/>
        <field name="act_window_id" ref="action_logistics_sale_quotations"/>
    </record>

    <record id="sale_order_action_view_logistics_quotation_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="sale.view_sale_order_pivot"/>
        <field name="act_window_id" ref="action_logistics_sale_quotations"/>
    </record>

    <record id="sale_order_action_view_logistics_quotation_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="6"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="sale.view_sale_order_graph"/>
        <field name="act_window_id" ref="action_logistics_sale_quotations"/>
    </record>


    
  </data>
</odoo>