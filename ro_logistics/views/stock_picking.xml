<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
  <data>

      <record id="stock_picking_logistics_fields_search_view" model="ir.ui.view">
        <field name="name">stock.picking.logistics_search</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_internal_search"/>
        <field name="arch" type="xml">
          <xpath expr="//field[@name='picking_type_id']" position="after">
            <field name="carrier_tracking_ref" />
          </xpath>
        </field>
      </record>

      <record id="stock_picking_logistics_fields_form_view" model="ir.ui.view">
          <field name="name">stock.picking.logistics_fields.form</field>
          <field name="model">stock.picking</field>
          <field name="inherit_id" ref="stock.view_picking_form"/>
          <field name="arch" type="xml">
            
            <xpath expr="//page[@name='extra']" position="after">

              <page name="logiistics" string="Logistics" invisible="sale_id == False" >
                  <field name="sale_id" invisible="1"/>
                <group>
                  <group>
                    <field name="cut_off_date" />
                    <field name="loading_date" />
                    <field name="etd_date" />
                    <field name="eta_date" />
                    <field name="ata_date" />
                  </group>
                  <group>
                    <field name="booking_number" />
                    <field name="bl_number" />
                    <field name="vessel_name" />
                    <!-- <field name="gln" />       -->
  
                    <field name="loading_port" />
                    <field name="destination_port" />
                    <field name="shipping_method_id"/>
                    <field name="shipping_status_id"/>

                    <field name="agent_id" />
                  </group>
                </group>
              </page>
            </xpath>
          </field>
      </record>

      <record id="stock_picking_logistics_fields_tree" model="ir.ui.view">
        <field name="name">stock.picking.logistics_fields.tree</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.vpicktree"/>
        <field name="arch" type="xml">
          
          <xpath expr="//field[@name='state']" position="before">
            <field name="cut_off_date" optional="hide" />
            <field name="loading_date" optional="hide" />
            <field name="etd_date" optional="hide" />
            <field name="eta_date" optional="hide" />
            <field name="ata_date" optional="hide" />

            <field name="booking_number" optional="hide" />
            <field name="bl_number" optional="hide" />
            <field name="vessel_name" optional="hide" />
            <field name="carrier_tracking_ref" optional="hide" />

            <field name="loading_port" optional="hide" />
            <field name="destination_port" optional="hide" />
            <field name="shipping_method_id" optional="hide"/>
            <field name="shipping_status_id" optional="hide"/>
            <field name="agent_id" optional="hide" />
          </xpath>
          
        </field>
    </record>
      
    

    <record id="stock_picking_type_logistcs_action" model="ir.actions.act_window">
      <field name="name">Inventory Overview</field>
      <field name="res_model">stock.picking.type</field>
      <field name="view_mode">kanban,form</field>
      <field name="domain">[('is_logistics', '=', True)]</field>
      <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
          Create a new operation type
          </p><p>
          The operation type system allows you to assign each stock
          operation a specific type which will alter its views accordingly.
          On the operation type you could e.g. specify if packing is needed by default,
          if it should show the customer.
          </p>
      </field>
    </record>



  </data>
</odoo>