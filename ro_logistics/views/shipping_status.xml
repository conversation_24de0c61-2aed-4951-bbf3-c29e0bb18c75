<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Shipping Status Config -->
        <record id="log_shipping_status_config_form_view" model="ir.ui.view">
            <field name="name">Shipping Status config.form</field>
            <field name="model">log.shipping.status</field>
            <field name="arch" type="xml">
                <form string="Shipping Status">
                    <sheet>
                        <group>
                            <group>
                                <field name="name" />
                            </group>
                            <group>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>


        <record id="log_shipping_status_config_tree_view" model="ir.ui.view">
            <field name="name">Shipping Status config.tree</field>
            <field name="model">log.shipping.status</field>
            <field name="arch" type="xml">
                <tree string="Shipping Status" editable="bottom">
                    <field name="name" />
                </tree>
            </field>
        </record>

        <!-- actions opening model views on models -->
        <record id="log_shipping_status_config_action_window" model="ir.actions.act_window">
            <field name="name">Shipping Status</field>
            <field name="res_model">log.shipping.status</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree</field>
        </record>    
        <menuitem action="log_shipping_status_config_action_window" id="log_shipping_status_config" sequence="65" parent="sale.menu_sale_config"/>
  

    </data>
</odoo>
