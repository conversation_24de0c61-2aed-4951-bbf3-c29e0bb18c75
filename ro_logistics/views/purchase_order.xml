<odoo>
  <data>

    <record id="purchase.purchase_rfq" model="ir.actions.act_window">
      <field name="domain">[('is_logistics', '=', False)]</field>
    </record>
    <record id="purchase.purchase_form_action" model="ir.actions.act_window">
        <field name="domain">[('is_logistics', '=', False), ('state','in',('purchase', 'done'))]</field>
    </record>


    <record id="action_logistics_quotations_list_view" model="ir.ui.view">
      <field name="name">purchase.order.logistics.list.view</field>
      <field name="model">purchase.order</field>
      <field name="priority">100</field>
      <field name="mode">primary</field>
      <field name="inherit_id" ref="purchase.purchase_order_kpis_tree"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='amount_total']" position="after">
            <field name="is_logistics" invisible="1"/>
          </xpath>

          <xpath expr="//field[@name='state']" position="before">
            <field name="cut_off_date" optional="hide" />
            <field name="loading_date" optional="hide" />
            <field name="etd_date" optional="hide" />
            <field name="eta_date" optional="hide" />
            <field name="ata_date" optional="hide" />

            <field name="booking_number" optional="hide" />
            <field name="bl_number" optional="hide" />
            <field name="vessel_name" optional="hide" />
            <field name="gln" optional="hide" />

            <field name="loading_port" optional="hide" />
            <field name="destination_port" optional="hide" />

            <field name="shipping_method_id" optional="hide" />
            <field name="shipping_status_id" optional="hide" />
            
            <field name="agent_id" optional="hide" />
          </xpath>
      
      </field>
  </record>

  <record id="action_logistics_quotations_form" model="ir.ui.view">
      <field name="name">purchase.order.logistics.form</field>
      <field name="model">purchase.order</field>
      <field name="priority">100</field>
      <field name="mode">primary</field>
      <field name="inherit_id" ref="purchase.purchase_order_form"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='partner_ref']" position="after">
              <field name="is_logistics" invisible="1"/>
          </xpath>

          <xpath expr="//page[@name='purchase_delivery_invoice']" position="after">
            <page name="logistics" string="Logistics">
              <group>
                <group>
                  <field name="cut_off_date" />
                  <field name="loading_date" />
                  <field name="etd_date" />
                  <field name="eta_date" />
                  <field name="ata_date" />
                </group>
                <group>
                  <field name="booking_number" />
                  <field name="bl_number" />
                  <field name="vessel_name" />
                  <field name="gln" />      

                  <field name="loading_port" />
                  <field name="destination_port" />
      
                  <field name="shipping_method_id"/>
                  <field name="shipping_status_id"/>

                  <field name="agent_id" />
                </group>
              </group>
            </page>
          </xpath>

      </field>
  </record>
  
  <record id="action_logistics_quotations" model="ir.actions.act_window">
      <field name="name">Logistics Order</field>
      <field name="type">ir.actions.act_window</field>
      <field name="res_model">purchase.order</field>
      <field name="view_mode">tree,kanban,form,calendar,pivot,graph,activity</field>
      <field name="search_view_id" ref="purchase.view_purchase_order_filter"/>
      <field name="view_ids"
          eval="[(5, 0, 0),
                  (0, 0, {'view_mode': 'tree', 'view_id': ref('action_logistics_quotations_list_view')}),
                  (0, 0, {'view_mode': 'form', 'view_id': ref('action_logistics_quotations_form')})]"/>
      <field name="context">{'default_is_logistics': True}</field>
      <field name="domain">[('is_logistics', '=', True)]</field>
      <!-- , ('state', '=', 'draft') -->
      <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
          Create a new quotation, the first step of a new Logistics!
          </p><p>
          Once the quotation is confirmed by the customer, it becomes a Logistics order.<br/> You will be able to create an invoice and collect the payment.
          </p>
      </field>
  </record>



  </data>
</odoo>
