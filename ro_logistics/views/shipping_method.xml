<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Shipping Methods Config -->
        <record id="log_shipping_methods_config_form_view" model="ir.ui.view">
            <field name="name">Shipping Methods config.form</field>
            <field name="model">log.shipping.method</field>
            <field name="arch" type="xml">
                <form string="Shipping Methods">
                    <sheet>
                        <group>
                            <group>
                                <field name="name" />
                            </group>
                            <group>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>


        <record id="log_shipping_methods_config_tree_view" model="ir.ui.view">
            <field name="name">Shipping Methods config.tree</field>
            <field name="model">log.shipping.method</field>
            <field name="arch" type="xml">
                <tree string="Shipping Methods" editable="bottom">
                    <field name="name" />
                </tree>
            </field>
        </record>

        <!-- actions opening model views on models -->
        <record id="log_shipping_methods_config_action_window" model="ir.actions.act_window">
            <field name="name">Shipping Methods</field>
            <field name="res_model">log.shipping.method</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree</field>
        </record>    
        <menuitem action="log_shipping_methods_config_action_window" id="log_shipping_methods_config" sequence="65" parent="sale.menu_sale_config"/>
  

    </data>
</odoo>
