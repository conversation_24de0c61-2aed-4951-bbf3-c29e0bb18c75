<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
  <data>
    
      <record id="stock_move_line_logistics_fields_form_view" model="ir.ui.view">
          <field name="name">stock.move.line.logistics_fields.form</field>
          <field name="model">stock.move.line</field>
          <field name="inherit_id" ref="stock.view_move_line_tree"/>
          <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="before">
              <field name="cut_off_date" optional="hide" />
              <field name="loading_date" optional="hide" />
              <field name="etd_date" optional="hide" />
              <field name="eta_date" optional="hide" />
              <field name="ata_date" optional="hide" />
  
              <field name="booking_number" optional="hide" />
              <field name="bl_number" optional="hide" />
              <field name="vessel_name" optional="hide" />
              <field name="gln" optional="hide" />
  
              <field name="loading_port" optional="hide" />
              <field name="destination_port" optional="hide" />
              <field name="shipping_method_id" optional="hide"/>
              <field name="shipping_status_id" optional="hide"/>

              <field name="agent_id" optional="hide" />
            </xpath>  
          </field>
      </record>

  </data>
</odoo>