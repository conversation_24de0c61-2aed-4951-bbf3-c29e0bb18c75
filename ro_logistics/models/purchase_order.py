# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.addons.purchase.models.purchase_order import PurchaseOrder


@api.model_create_multi
def create(self, vals_list):
    orders = self.browse()
    partner_vals_list = []
    for vals in vals_list:
        company_id = vals.get('company_id', self.default_get(['company_id'])['company_id'])
        # Ensures default picking type and currency are taken from the right company.
        self_comp = self.with_company(company_id)
        if vals.get('name', 'New') == 'New':
            seq_date = None
            if 'date_order' in vals:
                seq_date = fields.Datetime.context_timestamp(self, fields.Datetime.to_datetime(vals['date_order']))
            if 'is_logistics' in vals and vals['is_logistics']:
                vals['name'] = self_comp.env['ir.sequence'].next_by_code('logistics.order', sequence_date=seq_date) or '/'
            else:
                vals['name'] = self_comp.env['ir.sequence'].next_by_code('purchase.order', sequence_date=seq_date) or '/'
        vals, partner_vals = self._write_partner_values(vals)
        partner_vals_list.append(partner_vals)
        orders |= super(PurchaseOrder, self_comp).create(vals)
    for order, partner_vals in zip(orders, partner_vals_list):
        if partner_vals:
            order.sudo().write(partner_vals)  # Because the purchase user doesn't have write on `res.partner`
    return orders

PurchaseOrder.create = create

class PurchaseOrderInh(models.Model):
   _inherit = 'purchase.order'

   is_logistics = fields.Boolean()

   sale_log_id = fields.Many2one('sale.order')

   cut_off_date = fields.Date(related='sale_log_id.cut_off_date', store=True, readonly=False)
   loading_date = fields.Datetime(related='sale_log_id.loading_date', store=True, readonly=False)
   etd_date = fields.Date('ETD', related='sale_log_id.etd_date', store=True, readonly=False)
   eta_date = fields.Date('ETA', related='sale_log_id.eta_date', store=True, readonly=False)
   ata_date = fields.Date('ATA', related='sale_log_id.ata_date', store=True, readonly=False)

   booking_number = fields.Char('Booking Number', related='sale_log_id.booking_number', store=True, readonly=False)
   bl_number = fields.Char('BL Number', related='sale_log_id.bl_number', store=True, readonly=False)
   vessel_name = fields.Many2one('log.vessel', 'Vessel Name', related='sale_log_id.vessel_name', store=True, readonly=False)
   gln = fields.Char('Container Number', related='sale_log_id.gln', store=True, readonly=False)

   loading_port = fields.Many2one('res.port', related='sale_log_id.loading_port', store=True, readonly=False)
   destination_port = fields.Many2one('res.port', related='sale_log_id.destination_port', store=True, readonly=False)

   shipping_method_id = fields.Many2one('log.shipping.method', related='sale_log_id.shipping_method_id', store=True, readonly=False)
   shipping_status_id = fields.Many2one('log.shipping.status', related='sale_log_id.shipping_status_id', store=True, readonly=False)

   agent_id = fields.Many2one('res.partner', related='sale_log_id.agent_id', store=True, readonly=False)


class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    
    sale_line_logistic_id = fields.Many2one('logistic.purchase')


class PurchaseReport(models.Model):
   _inherit = 'purchase.report'

   is_logistics = fields.Boolean(readonly=True)

   cut_off_date = fields.Date(readonly=True)
   loading_date = fields.Datetime(readonly=True)
   etd_date = fields.Date('ETD', readonly=True)
   eta_date = fields.Date('ETA', readonly=True)
   ata_date = fields.Date('ATA', readonly=True)

   booking_number = fields.Char('Booking Number', readonly=True)
   bl_number = fields.Char('BL Number', readonly=True)
   vessel_name = fields.Many2one('log.vessel', 'Vessel Name', readonly=True)
   gln = fields.Char('Container Number', readonly=True)

   loading_port = fields.Many2one('res.port', readonly=True)
   destination_port = fields.Many2one('res.port', readonly=True)

   agent_id = fields.Many2one('res.partner', readonly=True)

   shipping_method_id = fields.Many2one('log.shipping.method', readonly=True)
   shipping_status_id = fields.Many2one('log.shipping.status', readonly=True)
   def _select(self):
      return super(PurchaseReport, self)._select() + ',po.is_logistics, po.cut_off_date, po.loading_date, po.etd_date, po.eta_date, po.ata_date, po.booking_number, po.bl_number, po.vessel_name, po.gln, po.loading_port, po.destination_port, po.shipping_method_id, po.shipping_status_id, po.agent_id'
   
   def _group_by(self):
      return super(PurchaseReport, self)._group_by() + ',po.is_logistics, po.cut_off_date, po.loading_date, po.etd_date, po.eta_date, po.ata_date, po.booking_number, po.bl_number, po.vessel_name, po.gln, po.loading_port, po.destination_port, po.shipping_method_id, po.shipping_status_id, po.agent_id'
