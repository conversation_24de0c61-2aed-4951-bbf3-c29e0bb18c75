# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'


    cut_off_date = fields.Date(related='picking_id.cut_off_date', store=True, readonly=False)
    loading_date = fields.Datetime(related='picking_id.loading_date', store=True, readonly=False)
    etd_date = fields.Date('ETD', related='picking_id.etd_date', store=True, readonly=False)
    eta_date = fields.Date('ETA', related='picking_id.eta_date', store=True, readonly=False)
    ata_date = fields.Date('ATA', related='picking_id.ata_date', store=True, readonly=False)

    booking_number = fields.Char('Booking Number', related='picking_id.booking_number', store=True, readonly=False)
    bl_number = fields.Char('BL Number', related='picking_id.bl_number', store=True, readonly=False)
    vessel_name = fields.Many2one('log.vessel', 'Vessel Name', related='picking_id.vessel_name', store=True, readonly=False)
    gln = fields.Char('Container Number', related='picking_id.carrier_tracking_ref', store=True, readonly=False)

    loading_port = fields.Many2one('res.port', related='picking_id.loading_port', store=True, readonly=False)
    destination_port = fields.Many2one('res.port', related='picking_id.destination_port', store=True, readonly=False)

    agent_id = fields.Many2one('res.partner', related='picking_id.agent_id', store=True, readonly=False)

    shipping_method_id = fields.Many2one('log.shipping.method', related='picking_id.shipping_method_id', store=True, readonly=False)
    shipping_status_id = fields.Many2one('log.shipping.status', related='picking_id.shipping_status_id', store=True, readonly=False)
