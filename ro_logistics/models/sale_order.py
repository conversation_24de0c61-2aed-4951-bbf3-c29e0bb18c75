# -*- coding: utf-8 -*-
from collections import defaultdict
from odoo import models, fields, api, _


class SaleOrder(models.Model):
   _inherit = 'sale.order'


   cut_off_date = fields.Date()
   loading_date = fields.Datetime()
   etd_date = fields.Date('ETD')
   eta_date = fields.Date('ETA')
   ata_date = fields.Date('ATA')

   booking_number = fields.Char('Booking Number')
   bl_number = fields.Char('BL Number')
   vessel_name = fields.Many2one('log.vessel', 'Vessel Name')
   gln = fields.Char('Container Number')

   loading_port = fields.Many2one('res.port')
   destination_port = fields.Many2one('res.port')

   shipping_method_id = fields.Many2one('log.shipping.method')

   shipping_status_id = fields.Many2one('log.shipping.status')

   agent_id = fields.Many2one('res.partner')


   consignee_id = fields.Many2one('res.partner')
   notify_id = fields.Many2one('res.partner','Notify')
   sec_notify_id = fields.Many2one('res.partner','Sec Notify')

   logistic_purchase_ids = fields.One2many('logistic.purchase', 'sale_id')

   po_logistics_count = fields.Integer(
        "Number of PO Logistcs",
        compute='_compute_po_logistics_count')
   po_logistics_ids = fields.One2many('purchase.order', 'sale_log_id')
   @api.depends('po_logistics_ids')
   def _compute_po_logistics_count(self):
      for rec in self:
         rec.po_logistics_count = len(rec.po_logistics_ids)

   def action_view_po_logistics(self):
      self.ensure_one()
      po_logistics_ids = self.po_logistics_ids.ids
      action = {
         'res_model': 'purchase.order',
         'type': 'ir.actions.act_window',
      }
      if len(po_logistics_ids) == 1:
         action.update({
               'view_mode': 'form',
               'res_id': po_logistics_ids[0],
         })
      else:
         action.update({
               'name': _('Generated Logistics %s', self.name),
               'domain': [('id', 'in', po_logistics_ids)],
               'view_mode': 'tree,form',
         })
      return action
   

   def create_logistic_purchase(self):
      for rec in self:

         # {'partenr_id': {'currency': [po_vals]}}
         purchase_values = defaultdict(lambda: defaultdict(list))

         for line in rec.logistic_purchase_ids.filtered(lambda x: not x.po_created):
            line.po_created = True
            purchase_values[line.vendor_id.id][line.currency_id.id].append({
               'product_id': line.product_id.id,
               'name': line.name,
               # 'sale_line_id': line.sale_order_line.id,
               'product_qty': line.quantity,
               'product_uom': line.product_uom.id,
               'price_unit': line.unit_price,
               'analytic_distribution': line.analytic_distribution,
               'sale_line_logistic_id': line.id
            })

         for partner_id in purchase_values: # For each partner_id.
            for currency_id in purchase_values[partner_id]: # For each currency_id'
      
               po = self.env['purchase.order'].create({
                  'partner_id': partner_id,
                  'currency_id': currency_id,
                  'sale_log_id': rec.id,
                  'origin': rec.name,
                  'is_logistics': True,
                  'picking_type_id':self.env["stock.picking.type"].search([("code", "=", "incoming"),("warehouse_id", "=", rec.warehouse_id.id),],limit=1,).id or False,
                  'order_line': [(0,0,comp) for comp in purchase_values[partner_id][currency_id] ]
               })


class SaleReport(models.Model):
   _inherit = 'sale.report'


   cut_off_date = fields.Date()
   loading_date = fields.Datetime()
   etd_date = fields.Date('ETD')
   eta_date = fields.Date('ETA')
   ata_date = fields.Date('ATA')

   booking_number = fields.Char('Booking Number')
   bl_number = fields.Char('BL Number')
   vessel_name = fields.Many2one('log.vessel', 'Vessel Name')
   gln = fields.Char('Container Number')

   loading_port = fields.Many2one('res.port')
   destination_port = fields.Many2one('res.port')

   agent_id = fields.Many2one('res.partner')

   shipping_method_id = fields.Many2one('log.shipping.method')

   shipping_status_id = fields.Many2one('log.shipping.status')

   consignee_id = fields.Many2one('res.partner')
   notify_id = fields.Many2one('res.partner','Notify')
   sec_notify_id = fields.Many2one('res.partner','Sec Notify')

   
   def _select_additional_fields(self):
      res = super()._select_additional_fields()
      res['cut_off_date'] = "s.cut_off_date"
      res['loading_date'] = "s.loading_date"
      res['etd_date'] = "s.etd_date"
      res['eta_date'] = "s.eta_date"
      res['ata_date'] = "s.ata_date"

      res['booking_number'] = "s.booking_number"
      res['bl_number'] = "s.bl_number"
      res['vessel_name'] = "s.vessel_name"
      res['gln'] = "s.gln"

      res['loading_port'] = "s.loading_port"
      res['destination_port'] = "s.destination_port"

      res['shipping_method_id'] = "s.shipping_method_id"
      res['shipping_status_id'] = "s.shipping_status_id"
      res['agent_id'] = "s.agent_id"
      res['consignee_id'] = "s.consignee_id"
      res['notify_id'] = "s.notify_id"
      res['sec_notify_id'] = "s.sec_notify_id"
      return res

   def _group_by_sale(self):
      res = super()._group_by_sale()
      res += """,
         s.cut_off_date,
         s.loading_date,
         s.etd_date,
         s.eta_date,
         s.ata_date,
         s.booking_number,
         s.bl_number,
         s.vessel_name,
         s.gln,
         s.loading_port,
         s.destination_port,
         s.shipping_method_id,
         s.shipping_status_id,
         s.agent_id,
         s.consignee_id,
         s.notify_id,
         s.sec_notify_id"""
      return res
