# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class LogisticPurchase(models.Model):
    _name = 'logistic.purchase'
    _description = 'Logistic Purchase'
    _inherit = ['analytic.mixin']

    name = fields.Char()

    sale_id = fields.Many2one('sale.order')

    product_template_id = fields.Many2one('product.template', string='Product Template', domain=[('purchase_ok', '=', True)])

    product_id = fields.Many2one('product.product', string='Product', domain="[('product_tmpl_id', '=', product_template_id),('purchase_ok', '=', True)]")

    vendor_id = fields.Many2one('res.partner')

    quantity = fields.Float(string='Quantity', digits='Product Unit of Measure', required=True, default=1)
    product_uom_category_id = fields.Many2one(related='product_id.uom_id.category_id')
    product_uom = fields.Many2one('uom.uom', domain="[('category_id', '=', product_uom_category_id)]")

    analytic_account_id = fields.Many2one('account.analytic.account', related='sale_id.analytic_account_id')

    unit_price = fields.Float(string='Unit Price', required=True, digits='Product Price')
    currency_id = fields.Many2one('res.currency')
    
    price_subtotal = fields.Float(string='Subtotal', compute='_compute_price_subtotal')

    po_created = fields.Boolean()



    analytic_distribution = fields.Json(
        'Analytic',
        compute="_compute_analytic_distribution", store=True, copy=True, readonly=False,
        precompute=True
    )
    @api.depends('analytic_account_id')
    def _compute_analytic_distribution(self):
        for rec in self:
            if rec.analytic_account_id:
                rec.analytic_distribution = {rec.analytic_account_id.id:100} 
    

    @api.onchange('product_id')
    def _onchange_product_id(self):
        internal_ref = self.product_id.default_code
        self.name = f"[{internal_ref}] {self.product_id.name}" if self.product_id else ''
        self.product_uom = self.product_id.uom_id
        self.unit_price = self.product_id.list_price

    @api.depends('product_id', 'quantity', 'unit_price')
    def _compute_price_subtotal(self):
        for rec in self:
            rec.price_subtotal = rec.quantity * rec.unit_price
