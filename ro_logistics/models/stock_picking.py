# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class StockPicking(models.Model):
    _inherit = 'stock.picking'


    cut_off_date = fields.Date(related='sale_id.cut_off_date', store=True, readonly=False)
    loading_date = fields.Datetime(related='sale_id.loading_date', store=True, readonly=False)
    etd_date = fields.Date('ETD', related='sale_id.etd_date', store=True, readonly=False)
    eta_date = fields.Date('ETA', related='sale_id.eta_date', store=True, readonly=False)
    ata_date = fields.Date('ATA', related='sale_id.ata_date', store=True, readonly=False)

    booking_number = fields.Char('Booking Number', related='sale_id.booking_number', store=True, readonly=False)
    bl_number = fields.Char('BL Number', related='sale_id.bl_number', store=True, readonly=False)
    vessel_name = fields.Many2one('log.vessel', 'Vessel Name', related='sale_id.vessel_name', store=True, readonly=False)
    # gln = fields.Char('GLN', related='sale_id.gln', store=True, readonly=False)
    carrier_tracking_ref = fields.Char(related='sale_id.gln', store=True, readonly=False)
    loading_port = fields.Many2one('res.port', related='sale_id.loading_port', store=True, readonly=False)
    destination_port = fields.Many2one('res.port', related='sale_id.destination_port', store=True, readonly=False)

    agent_id = fields.Many2one('res.partner', related='sale_id.agent_id', store=True, readonly=False)

    shipping_method_id = fields.Many2one('log.shipping.method', related='sale_id.shipping_method_id', store=True, readonly=False)
    shipping_status_id = fields.Many2one('log.shipping.status', related='sale_id.shipping_status_id', store=True, readonly=False)
