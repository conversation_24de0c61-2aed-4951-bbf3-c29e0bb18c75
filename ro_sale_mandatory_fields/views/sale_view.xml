<odoo>
    <data>
  <record id="sale_order_logistics_fields_form_view_mandatory_fields" model="ir.ui.view">
        <field name="name">sale.order.inherit.logistics.mandatory.fields</field>
        <field name="model">sale.order</field>
        <!-- <field name="priority">18</field> -->
        <field name="inherit_id" ref="ro_logistics.sale_order_logistics_fields_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='incoterm']" position="attributes">
                
              <attribute name="required">1</attribute>
                
            </xpath> 
           
            
          
        </field>
    </record>
    <record id="view_order_form_inherit_mandatory_fields" model="ir.ui.view">
        <field name="name">sale.order.inherit.mandatory.fields</field>
        <field name="model">sale.order</field>
        <!-- <field name="priority">18</field> -->
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payment_term_id']" position="attributes">
                
              <attribute name="required">1</attribute>
                
            </xpath> 
            <!-- <xpath expr="//field[@name='order_line']//field[@name='price_unit']" position="attributes">
                
              <attribute name="required">1</attribute>
                
            </xpath>  -->
            <!-- <xpath expr="//field[@name='tag_ids']" position="attributes">
                
              <attribute name="required">1</attribute>
                
            </xpath>  -->
           
            
          
        </field>
    </record>

    </data>
</odoo>
