# -*- coding: utf-8 -*-
{
    'name': "Sale Mandatory Fields",

    'summary': "sale price ,payment term ,incoterm  always required and  mandatory",

    'description': """
            sale price ,payment term ,incoterm  always required and  mandatory
    """,

    'author': "Roayatec",
    'website': "https://www.roayatec.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'sale',
    'version': '17.0',

    # any module necessary for this one to work correctly
    'depends': ['base','sale','ro_logistics'],

    # always loaded
    'data': [
        # 'security/ir.model.access.csv',
        'views/sale_view.xml',
       
    ],
    'license': 'OPL-1', 
}

