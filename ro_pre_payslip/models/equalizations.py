
from odoo import fields, models,api,_

class Equalizations(models.Model):
    _name = "hr.equalizations"
    _description = "equalizations"

    name = fields.Char(string='name')
    ro_equalizations_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_equalizations = fields.Float('تسويات')
    
    date = fields.Date(default=fields.Date.context_today, required=True)

 
    

   
