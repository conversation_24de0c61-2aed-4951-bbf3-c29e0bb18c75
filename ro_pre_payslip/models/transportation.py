
from odoo import fields, models,api,_

class Transportation(models.Model):
    _name = "hr.transportation"
    _description = "transportation"

    name = fields.Char(string='name')
    ro_transportation_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_transportation = fields.Float('بدل أنتقال')

 
    date = fields.Date(default=fields.Date.context_today, required=True)
    

   
