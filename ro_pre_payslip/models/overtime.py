
from odoo import fields, models,api,_
from odoo.exceptions import ValidationError
from odoo.osv import expression


class Overtime(models.Model):
    _name = "hr.overtime"
    _description = "Overtime"

    name = fields.Char(string='name')
    ro_overtime_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_overtime = fields.Float('Overtime')

   
    date = fields.Date(default=fields.Date.context_today, required=True)