
from odoo import fields, models,api,_

class MedicalAllowance(models.Model):
    _name = "hr.medical.allowance"
    _description = "hr medical allowance"

    name = fields.Char(string='name')
    ro_medical_allowance_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_medical_allowance = fields.Float('بدل علاج')

   
    date = fields.Date(default=fields.Date.context_today, required=True)
