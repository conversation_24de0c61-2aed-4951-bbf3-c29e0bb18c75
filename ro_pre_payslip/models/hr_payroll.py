from odoo import models, fields,api

class Payslip(models.Model):
    _inherit = 'hr.payslip'

    
    ro_overtime = fields.Float('Overtime', compute='_compute_ro_overtime')
    # overtime_id = fields.Many2one('hr.overtime', string='Overtime')
    ro_incentive = fields.Float(string='منح',compute='_compute_ro_incentive')
    ro_medical_allowance = fields.Float('بدل علاج',compute='_compute_ro_medical_allowance')
    ro_food_allowance = fields.Float('بدل وجبات',compute='_compute_ro_food_allowance')
    ro_transportation = fields.Float('بدل أنتقال',compute='_compute_ro_transportation')
    ro_equalizations = fields.Float('تسويات',compute='_compute_ro_equalizations')
    ro_rewards = fields.Float('مكافأت',compute='_compute_ro_rewards')
    ro_delays = fields.Float('تأخيرات',compute='_compute_ro_delays')

    
    




    

    @api.depends('employee_id')
    def _compute_ro_overtime(self):
        for payslip in self:
        
            if payslip.employee_id:
                overtime = self.env['hr.overtime'].search([
                    ('ro_overtime_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                ])
                payslip.ro_overtime = sum(overtime.mapped('ro_overtime'))
            else:
                payslip.ro_overtime = 0.0

    @api.depends('employee_id')
    def _compute_ro_incentive(self):
        for payslip in self:
        
            if payslip.employee_id:
                incentive = self.env['hr.incentive'].search([
                    ('ro_incentive_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                ])
                payslip.ro_incentive = sum(incentive.mapped('ro_incentive'))
            else:
                payslip.ro_incentive = 0.0

    @api.depends('employee_id')
    def _compute_ro_medical_allowance(self):
        for payslip in self:
        
            if payslip.employee_id:
                medical_allowance = self.env['hr.medical.allowance'].search([
                    ('ro_medical_allowance_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                ])
                payslip.ro_medical_allowance = sum(medical_allowance.mapped('ro_medical_allowance'))
            else:
                payslip.ro_medical_allowance = 0.0   

   
    @api.depends('employee_id')
    def _compute_ro_food_allowance(self):
        for payslip in self:
        
            if payslip.employee_id:
                food_allowance = self.env['hr.food.allowance'].search([
                    ('ro_food_allowance_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                ])
                payslip.ro_food_allowance = sum(food_allowance.mapped('ro_food_allowance'))
            else:
                payslip.ro_food_allowance = 0.0      


    @api.depends('employee_id')
    def _compute_ro_transportation(self):
        for payslip in self:
        
            if payslip.employee_id:
                transportation = self.env['hr.transportation'].search([
                    ('ro_transportation_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                ])
                payslip.ro_transportation = sum(transportation.mapped('ro_transportation'))
            else:
                payslip.ro_transportation = 0.0 



    @api.depends('employee_id')
    def _compute_ro_equalizations(self):
        for payslip in self:
        
            if payslip.employee_id:
                equalizations = self.env['hr.equalizations'].search([
                    ('ro_equalizations_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                ])
                payslip.ro_equalizations = sum(equalizations.mapped('ro_equalizations'))
            else:
                payslip.ro_equalizations = 0.0   



    @api.depends('employee_id')
    def _compute_ro_rewards(self):
        for payslip in self:
        
            if payslip.employee_id:
                rewards = self.env['hr.rewards'].search([
                    ('ro_rewards_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                ])
                payslip.ro_rewards = sum(rewards.mapped('ro_rewards'))
            else:
                payslip.ro_rewards = 0.0 


    @api.depends('employee_id')
    def _compute_ro_delays(self):
        for payslip in self:
        
            if payslip.employee_id:
                delays = self.env['hr.employee.delays'].search([
                    ('ro_delays_employee', '=', payslip.employee_id.id),
                    ('date', '>=', payslip.date_from),
                    ('date', '<=', payslip.date_to),
                    ('state','=','active')
                ])
                payslip.ro_delays = sum(delays.mapped('ro_delays'))
            else:
                payslip.ro_delays = 0.0                                                                         

    

   