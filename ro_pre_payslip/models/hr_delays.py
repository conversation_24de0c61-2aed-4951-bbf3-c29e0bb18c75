
# from odoo import fields, models,api,_

# class HRD<PERSON>y(models.Model):
#     _name = "hr.delay"
#     _description = "delay"

#     name = fields.Char(string='name')
#     ro_delays_employee = fields.Many2one('hr.employee', string='Employee',required=True)
#     ro_delays = fields.Float('تأخيرات')
    
    

 
    

   

# from odoo import fields, models,api,_

# class HrDelays(models.Model):
#     _name = "hr.delay"
#     _description = "Delays"

#     name = fields.Char(string='name')
#     ro_delays_employee = fields.Many2one('hr.employee', string='Employee',required=True)
#     ro_delays = fields.Float('تأخيرات')




   

    

   
