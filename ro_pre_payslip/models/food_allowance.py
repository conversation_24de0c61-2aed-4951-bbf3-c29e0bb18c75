
from odoo import fields, models,api,_

class FoodAllowance(models.Model):
    _name = "hr.food.allowance"
    _description = "food allowance"

    name = fields.Char(string='name')
    ro_food_allowance_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_food_allowance = fields.Float('بدل وجبات')
    
    date = fields.Date(default=fields.Date.context_today, required=True)
   
