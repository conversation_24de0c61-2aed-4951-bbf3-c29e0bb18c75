
from odoo import fields, models,api,_

class EmployeeDelays(models.Model):
    _name = "hr.employee.delays"
    _description = "hr delays"

    date = fields.Date(default=fields.Date.context_today, required=True)
    name = fields.Char(string='name')
    ro_delays_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_delays = fields.Float('تأخيرات')
    state = fields.Selection([('active', 'Active'), ('cancel', 'Cancel')], default='active', required=True)
   

    def action_cancel(self):
        for rec in self:
            rec.state = 'cancel'