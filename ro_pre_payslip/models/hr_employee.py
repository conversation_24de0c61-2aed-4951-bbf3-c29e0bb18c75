
from odoo import fields, models,api,_
from odoo.exceptions import ValidationError
from odoo.osv import expression



class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        """
        name search that supports searching by tag code
        """
        args = args or []
        domain = []
        if name:
            domain = ['|', ('registration_number', 'ilike', name), ('name', operator, name)]
            if operator in expression.NEGATIVE_TERM_OPERATORS:
                domain = ['&'] + domain
        state = self.search(domain + args, limit=limit)
        return state.name_get()

    def name_get(self):
        employee_list = []
        for this in self:
            if this.registration_number:
                name = '%s [%s]' % (this.name or '', this.registration_number or '')
            else:
                name =  this.name or ''
            employee_list.append((this.id, name))
        return employee_list
    
   