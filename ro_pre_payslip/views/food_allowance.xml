<odoo>
  <data>



        <record id="hr_prepayslip_tree_view_food_allowance" model="ir.ui.view">
            <field name="name">hr.food.allowance.view.tree</field>
            <field name="model">hr.food.allowance</field>
            <field name="arch" type="xml">
                <tree string="food allowance">
                   
                    
                    <!-- <field name="name"/> -->
                    <field name="date"/>
                    <field name="ro_food_allowance_employee"/>
                    <!-- <field name="state" widget="badge" select="1" readonly="1"/> -->


                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_food_allowance" model="ir.ui.view">
            <field name="name">food.allowance.view.form</field>
            <field name="model">hr.food.allowance</field>
            <field name="arch" type="xml">
                <form string="food allowance">
                <sheet>
                        <group name="food allowance" string="food allowance">
                            <group>
                                <field name="ro_food_allowance_employee"></field>
                                <field name="ro_food_allowance"></field>
                            </group>
                            <group>
                                <field name="date"/>
                            </group>
                        </group>
                </sheet>
                  </form>
            </field>
        </record>          
         

    <record id="hr_food_allowance_action" model="ir.actions.act_window">
        <field name="name">food allowance</field>
        <field name="res_model">hr.food.allowance</field>
        <field name="view_id" ref="prepayslip_form_view_food_allowance"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_food_allowance"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create food allowance
                </p>
            </field>
        <!-- <field name="search_view_id" ref="view_hr_payslip_filter"/> -->
       
    </record>
   


     <menuitem
        id="menu_hr_payroll_employee_food_allowance"
        name="food allowance"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="22"
        action="ro_pre_payslip.hr_food_allowance_action"
       />   
  </data>
</odoo>
