<odoo>
  <data>



        <record id="hr_prepayslip_tree_view_incentive" model="ir.ui.view">
            <field name="name">hr.incentive.view.tree</field>
            <field name="model">hr.incentive</field>
            <field name="arch" type="xml">
                <tree string="incentive">
                   
                    
                    <!-- <field name="name"/> -->
                    <field name="date"/>
                    <field name="ro_incentive_employee"/>
                    <!-- <field name="state" widget="badge" select="1" readonly="1"/> -->


                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_incentive" model="ir.ui.view">
            <field name="name">pre.payslip.view.incentive.form</field>
            <field name="model">hr.incentive</field>
            <field name="arch" type="xml">
                <form string="incentive">
                    <sheet>
                        <group name="incentive_group" string="incentive">
                            <group>
                                <field name="ro_incentive_employee"></field>
                                <field name="ro_incentive"></field>
                            </group>
                            <group>
                                <field name="date"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>          
         

    <record id="hr_incentive_action" model="ir.actions.act_window">
        <field name="name">incentive</field>
        <field name="res_model">hr.incentive</field>
        <field name="view_id" ref="prepayslip_form_view_incentive"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_incentive"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create incentive
                </p>
            </field>
        <!-- <field name="search_view_id" ref="view_hr_payslip_filter"/> -->
       
    </record>
   <!-- <menuitem
        id="menu_hr_payroll_employee_o"
        name="pre payslip"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
        sequence="70"
      
       /> -->


     <menuitem
        id="menu_hr_payroll_employee_intencive"
        name="incentive"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="21"
        action="ro_pre_payslip.hr_incentive_action"
       />   
  </data>
</odoo>
