<odoo>
  <data>

    <record id="action_cancel_delay" model="ir.actions.server">
        <field name="name">Cancel</field>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="model_id" ref="ro_pre_payslip.model_hr_employee_delays"/>
        <field name="binding_model_id" ref="ro_pre_payslip.model_hr_employee_delays"/>
        <field name="binding_view_types">list</field>
        <field name="code">records.action_cancel()</field>
    </record>


    <record id="hr_prepayslip_tree_view_employee_delays" model="ir.ui.view">
        <field name="name">employee.delays.view.tree</field>
        <field name="model">hr.employee.delays</field>
        <field name="arch" type="xml">
            <tree string="employee delays">
                
                <field name="date"/>
                <field name="ro_delays_employee"/>
                <field name="ro_delays"></field>
                <field name="state" widget="badge" decoration-success="state == 'active'"/>
                
            </tree>
        </field>
    </record>

    <record id="prepayslip_form_view_employee_delays" model="ir.ui.view">
        <field name="name">pre.payslip.view.employee.delays.form</field>
        <field name="model">hr.employee.delays</field>
        <field name="arch" type="xml">
            <form string="employee delays">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="active,cancel"/>
                </header>
                <sheet>
                    <group name="employee_dlays_group" string="delays">
                        <group>
                            <field name="ro_delays_employee"></field>
                            <field name="ro_delays"></field>
                        </group>
                        <group>
                            <field name="date"></field>
                        </group>
                    </group>
                </sheet>
                </form>
        </field>
    </record>          
         

    <record id="hr_employee_delays_action" model="ir.actions.act_window">
        <field name="name">delay</field>
        <field name="res_model">hr.employee.delays</field>
        <field name="view_id" ref="prepayslip_form_view_employee_delays"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_employee_delays"/>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create incentive
            </p>
        </field>
    </record>
   <!-- <menuitem
        id="menu_hr_payroll_employee_o"
        name="pre payslip"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
        sequence="70"
      
       /> -->


     <menuitem
        id="menu_hr_payroll_employee_delays"
        name="Delays"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="23"
        action="ro_pre_payslip.hr_employee_delays_action"
       />   
  </data>
</odoo>
