<odoo>
  <data>



        <record id="hr_prepayslip_tree_view_overtime" model="ir.ui.view">
            <field name="name">hr.prepayslip.view.tree</field>
            <field name="model">hr.overtime</field>
            <field name="arch" type="xml">
                <tree string="overtime">
                   
                    
                    <field name="date"/>
                    <field name="ro_overtime_employee"/>
                    <!-- <field name="state" widget="badge" select="1" readonly="1"/> -->


                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_overtime" model="ir.ui.view">
            <field name="name">pre.payslip.view.form</field>
            <field name="model">hr.overtime</field>
            <field name="arch" type="xml">
                <form string="overtime">
                    <sheet>
                        <group name="overtime_group" string="Overtime">
                            <group>
                                <field name="ro_overtime_employee"></field>
                                <field name="ro_overtime"></field>
                            </group>
                            <group>
                                <field name="date"></field>
                            </group>
                        </group>
                    </sheet>
                  </form>
            </field>
        </record>          
         

    <record id="hr_overtime_action" model="ir.actions.act_window">
        <field name="name">overtime</field>
        <field name="res_model">hr.overtime</field>
        <field name="view_id" ref="prepayslip_form_view_overtime"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_overtime"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create Pre payslip
                </p>
            </field>
        <!-- <field name="search_view_id" ref="view_hr_payslip_filter"/> -->
       
    </record>
   <menuitem
        id="menu_hr_payroll_employee_ovrtime"
        name="pre payslip"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
        sequence="70"
      
       />


     <menuitem
        id="menu_hr_payroll_employee_overtime"
        name="overtime"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="20"
        action="ro_pre_payslip.hr_overtime_action"
       />   
  </data>
</odoo>
