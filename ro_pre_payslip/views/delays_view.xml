<!-- <odoo>
  <data>



        <record id="hr_prepayslip_tree_view_delay" model="ir.ui.view">
            <field name="name">hr.delays.view.tree</field>
            <field name="model">hr.delay</field>
            <field name="arch" type="xml">
                <tree string="delays">
                   
                    
                  
                    <field name="ro_delays_employee"/>
                   

                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_delay" model="ir.ui.view">
            <field name="name">delays.view.form</field>
            <field name="model">hr.delay</field>
            <field name="arch" type="xml">
                <form string="delays">
                <sheet>
                        <group name="delays" string="delays">
                            <field name="ro_delays_employee"></field>
                            <field name="ro_delays"></field>

                        </group>
                </sheet>
                  </form>
            </field>
        </record>          
         

    <record id="hr_delay_action" model="ir.actions.act_window">
        <field name="name">delays</field>
        <field name="res_model">hr.delay</field>
        <field name="view_id" ref="prepayslip_form_view_delay"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_delay"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create delays
                </p>
            </field>
      
       
    </record>
   
     <menuitem
        id="menu_hr_payroll_hr_delay"
        name="Delays"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="22"
        action="ro_pre_payslip.hr_delay_action"
       />   
 

    
  </data>
</odoo> -->
