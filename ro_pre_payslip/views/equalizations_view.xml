<odoo>
  <data>



        <record id="hr_prepayslip_tree_view_equalizations" model="ir.ui.view">
            <field name="name">hr.equalizations.view.tree</field>
            <field name="model">hr.equalizations</field>
            <field name="arch" type="xml">
                <tree string="equalizations">
                   
                    
                    <!-- <field name="name"/> -->
                    <field name="date"/>
                    <field name="ro_equalizations_employee"/>
                    <!-- <field name="state" widget="badge" select="1" readonly="1"/> -->


                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_equalizations" model="ir.ui.view">
            <field name="name">equalizations.view.form</field>
            <field name="model">hr.equalizations</field>
            <field name="arch" type="xml">
                <form string="equalizations">
                    <sheet>
                        <group name="equalizations" string="equalizations">
                            <group>
                                <field name="ro_equalizations_employee"></field>
                                <field name="ro_equalizations"></field>
                            </group>
                            <group>
                                <field name="date"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>          
         

    <record id="hr_equalizations_action" model="ir.actions.act_window">
        <field name="name">equalizations</field>
        <field name="res_model">hr.equalizations</field>
        <field name="view_id" ref="prepayslip_form_view_equalizations"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_equalizations"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create equalizations
                </p>
            </field>
        <!-- <field name="search_view_id" ref="view_hr_payslip_filter"/> -->
       
    </record>
   


     <menuitem
        id="menu_hr_payroll_employee_equalizations"
        name="equalizations"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="22"
        action="ro_pre_payslip.hr_equalizations_action"
       />   
  </data>
</odoo>
