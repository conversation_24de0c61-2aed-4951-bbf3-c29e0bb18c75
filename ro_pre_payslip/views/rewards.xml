<odoo>
  <data>



        <record id="hr_prepayslip_tree_view_rewards" model="ir.ui.view">
            <field name="name">hr.rewards.view.tree</field>
            <field name="model">hr.rewards</field>
            <field name="arch" type="xml">
                <tree string="rewards">
                   
                    
                    <!-- <field name="name"/> -->
                    <field name="date"/>
                    <field name="ro_rewards_employee"/>
                    <!-- <field name="state" widget="badge" select="1" readonly="1"/> -->


                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_rewards" model="ir.ui.view">
            <field name="name">rewards.view.form</field>
            <field name="model">hr.rewards</field>
            <field name="arch" type="xml">
                <form string="rewards">
                    <sheet>
                        <group name="rewards" string="rewards">
                            <group>
                                <field name="ro_rewards_employee"></field>
                                <field name="ro_rewards"></field>
                            </group>
                            <group>
                                <field name="date"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>          
         

    <record id="hr_rewards_action" model="ir.actions.act_window">
        <field name="name">rewards</field>
        <field name="res_model">hr.rewards</field>
        <field name="view_id" ref="prepayslip_form_view_rewards"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_rewards"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create rewards
                </p>
            </field>
        <!-- <field name="search_view_id" ref="view_hr_payslip_filter"/> -->
       
    </record>
   


     <menuitem
        id="menu_hr_payroll_employee_rewards"
        name="rewards"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="22"
        action="ro_pre_payslip.hr_rewards_action"
       />   
  </data>
</odoo>
