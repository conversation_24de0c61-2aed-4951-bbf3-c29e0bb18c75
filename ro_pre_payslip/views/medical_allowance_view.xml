<odoo>
  <data>



        <record id="hr_prepayslip_tree_view_medical_allowance" model="ir.ui.view">
            <field name="name">hr.medical_allowance.view.tree</field>
            <field name="model">hr.medical.allowance</field>
            <field name="arch" type="xml">
                <tree string="medical allowance">
                   
                    
                    <!-- <field name="name"/> -->
                    <field name="date"/>
                    <field name="ro_medical_allowance_employee"/>
                    <!-- <field name="state" widget="badge" select="1" readonly="1"/> -->


                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_medical_allowance" model="ir.ui.view">
            <field name="name">medical_allowance.view.form</field>
            <field name="model">hr.medical.allowance</field>
            <field name="arch" type="xml">
                <form string="medical allowance">
                    <sheet>
                        <group name="medical_allowance" string="medical allowance">
                            <group>
                                <field name="ro_medical_allowance_employee"></field>
                                <field name="ro_medical_allowance"></field>
                            </group>
                            <group>
                                <field name="date"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>          
         

    <record id="hr_medical_allowance_action" model="ir.actions.act_window">
        <field name="name">medical allowance</field>
        <field name="res_model">hr.medical.allowance</field>
        <field name="view_id" ref="prepayslip_form_view_medical_allowance"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_medical_allowance"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create medical_allowance
                </p>
            </field>
        <!-- <field name="search_view_id" ref="view_hr_payslip_filter"/> -->
       
    </record>
   


     <menuitem
        id="menu_hr_payroll_employee_medical_allowance"
        name="medical allowance"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="22"
        action="ro_pre_payslip.hr_medical_allowance_action"
       />   
  </data>
</odoo>
