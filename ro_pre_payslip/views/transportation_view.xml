<odoo>
  <data>



        <record id="hr_prepayslip_tree_view_transportation" model="ir.ui.view">
            <field name="name">hr.transportation.view.tree</field>
            <field name="model">hr.transportation</field>
            <field name="arch" type="xml">
                <tree string="transportation">
                   
                    
                    <!-- <field name="name"/> -->
                    <field name="date"/>
                    <field name="ro_transportation_employee"/>
                    <!-- <field name="state" widget="badge" select="1" readonly="1"/> -->


                    
                </tree>
            </field>
        </record>
   
        <record id="prepayslip_form_view_transportation" model="ir.ui.view">
            <field name="name">transportation.view.form</field>
            <field name="model">hr.transportation</field>
            <field name="arch" type="xml">
                <form string="transportation">
                    <sheet>
                        <group name="transportation" string="transportation">
                            <group>
                                <field name="ro_transportation_employee"></field>
                                <field name="ro_transportation"></field>
                            </group>
                            <group>
                                <field name="date"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>          
         

    <record id="hr_transportation_action" model="ir.actions.act_window">
        <field name="name">transportation</field>
        <field name="res_model">hr.transportation</field>
        <field name="view_id" ref="prepayslip_form_view_transportation"/>
        <field name="view_id" ref="hr_prepayslip_tree_view_transportation"/>

            <field name="view_mode">tree,form</field>
        
         <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create transportation
                </p>
            </field>
        <!-- <field name="search_view_id" ref="view_hr_payslip_filter"/> -->
       
    </record>
   


     <menuitem
        id="menu_hr_payroll_employee_transportation"
        name="transportation"
        parent="ro_pre_payslip.menu_hr_payroll_employee_ovrtime"
        sequence="22"
        action="ro_pre_payslip.hr_transportation_action"
       />   
  </data>
</odoo>
