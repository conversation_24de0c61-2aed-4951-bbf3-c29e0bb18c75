
from odoo import models, fields, api,_
from odoo.exceptions import UserError,ValidationError
from collections import defaultdict




class SaleOrder(models.Model):
    _inherit = 'sale.order'
    ro_purchase_order_count = fields.Integer(string='Purchase Order Count', compute='_compute_purchase_order_counts')
    ro_purchase_ids = fields.One2many('purchase.order','ro_so_id', copy=False)
    
    @api.depends('ro_purchase_ids')
    def _compute_purchase_order_counts(self):
        for record in self:
            record.ro_purchase_order_count = len(record.ro_purchase_ids)
                
    def ro_action_view_purchase_orders(self):
        self.ensure_one()
        purchase_orders = self.ro_purchase_ids
        result = self.env['ir.actions.act_window']._for_xml_id('purchase.purchase_rfq')
        if len(purchase_orders) > 1:
            result['domain'] = [('id', 'in', purchase_orders.ids)]
        elif len(purchase_orders) == 1:
            result['views'] = [(self.env.ref('purchase.purchase_order_form', False).id, 'form')]
            result['res_id'] = purchase_orders.id
        else:
            result = {'type': 'ir.actions.act_window_close'}
        return result
    


            
    def ro_action_create_po(self):
        vendors_products = defaultdict(list)
        
        for order_line in self.order_line:
            product = order_line.product_id
            seller_id = product.seller_ids and product.seller_ids[0]
            
            if not seller_id:
                raise ValidationError(_('Please add Vendor'))
            else:
                price_unit = seller_id.price if seller_id.price else product.standard_price
                vendors_products[seller_id.partner_id.id].append({
                    'product_id':product.id,
                    'product_qty': order_line.product_uom_qty,
                    'product_uom': order_line.product_uom.id,
                    'price_unit': price_unit,
                    
                    'currency_id':self.env.ref('base.USD').id,
                    'ro_so_line_id':order_line.id,
                    
                                                    })
                
            
        for vendor, products in vendors_products.items():
            
            purchase_order_vals = {
                'partner_id': vendor,
                'date_order': fields.Datetime.now(),
                'ro_so_id':self.id,
                
                'currency_id':self.env.ref('base.USD').id,
                
                'order_line': [(0 ,0 ,{
                    'product_id': product['product_id'],
                    'product_qty': product['product_qty'],
                    'product_uom': product['product_uom'],
                    'price_unit': product['price_unit'],
                    'ro_so_line_id': product['ro_so_line_id'],
                    })
                        for product in products]
            }
            self.env['purchase.order'].create(purchase_order_vals)
        
        return self.ro_action_view_purchase_orders()
                
                
    
     
            


    