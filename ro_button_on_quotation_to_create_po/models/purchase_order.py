from odoo import models, fields, api,_
from odoo.exceptions import UserError,ValidationError
from collections import defaultdict


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
   
    ro_so_id = fields.Many2one('sale.order')
    ro_purchase_order_count = fields.Integer('ro_so_id.ro_purchase_order_count')
    
    
    
    def ro_action_preview_sale_order(self):
        return {
            'name': _('Sale Order'),
            'view_mode': 'form',
            'view_id': self.env.ref('sale.view_order_form').id,
            'res_model': 'sale.order',
            'type': 'ir.actions.act_window',
            'res_id': self.ro_so_id.id,
        }
