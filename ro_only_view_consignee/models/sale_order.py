# -*- coding: utf-8 -*-
from odoo import models, fields, api, _

class SaleOrder(models.Model):
   _inherit = 'sale.order'
   
   ro_is_consignee= fields.<PERSON><PERSON>an(related='partner_id.ro_is_consignee')
    
   consignee_id = fields.Many2one('res.partner',domain="[('ro_is_consignee', '=', True)]")
  
   ro_is_notify= fields.<PERSON><PERSON>an(related='partner_id.ro_is_notify')
   
   notify_id = fields.Many2one('res.partner','Notify',domain="[('ro_is_notify', '=', True)]")
   
   ro_is_sec_notify= fields.<PERSON><PERSON>an(related='partner_id.ro_is_sec_notify')

   sec_notify_id = fields.Many2one('res.partner','Sec Notify',domain="[('ro_is_sec_notify', '=', True)]") 
