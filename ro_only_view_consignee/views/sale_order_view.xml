<odoo>
    <data>
  <record id="view_order_form_inherit_consignee" model="ir.ui.view">
        <field name="name">sale.order.inherit</field>
        <field name="model">sale.order</field>
        <!-- <field name="priority">18</field> -->
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='consignee_id']" position="after">
                <field name="ro_is_consignee" invisible="1"/>
                <field name="ro_is_notify" invisible="1"/>
                <field name="ro_is_sec_notify" invisible="1"/>


                
            </xpath> 
            <xpath expr="//field[@name='consignee_id']" position="attributes">
                <attribute name="domain">
                [('ro_is_consignee', '=', True)]
               

                </attribute>
            </xpath>
            <xpath expr="//field[@name='notify_id']" position="attributes">
                <attribute name="domain">
                [('ro_is_notify', '=', True)]
               

                </attribute>
            </xpath>
            <xpath expr="//field[@name='sec_notify_id']" position="attributes">
                <attribute name="domain">
                [('ro_is_sec_notify', '=', True)]
               

                </attribute>
            </xpath>
            
          
        </field>
    </record>

    </data>
</odoo>
