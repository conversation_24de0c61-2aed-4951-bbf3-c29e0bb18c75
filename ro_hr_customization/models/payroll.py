from odoo import models, fields,api

class Payslip(models.Model):
    _inherit = 'hr.payslip'

    #allowances
    ro_overtime = fields.Float('Overtime')
    ro_incentive = fields.Float(string='منح')
    ro_medical_allowance = fields.Float('بدل علاج')
    ro_food_allowance = fields.Float('بدل وجبات')
    ro_transportation = fields.Float('بدل أنتقال')
    ro_equalizations = fields.Float('تسويات')
    ro_rewards = fields.Float('مكافأت')
    
    
 
    # deductions
    ro_absence = fields.Float('إجازة بدون رصيد')
    ro_penalties= fields.Float('جزائات')
    ro_splited_loans= fields.Float('سلف طويلة')
    ro_loans= fields.Float('سلف')
    # ro_discount_returns= fields.Float('تأمين طبي للأسره')
