<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="view_hr_payslip_form_inherit_add_fields" model="ir.ui.view">
            <field name="name">hr.payslip.view.form.inherit</field>
            <field name="model">hr.payslip</field>
            <field name="inherit_id" ref="hr_payroll.view_hr_payslip_form"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet/group" position="after">
                   <group>
                        <group name="Allowances">
                            <field name="ro_overtime" />
                            <field name="ro_incentive" />
                            <field name="ro_medical_allowance" />
                            <field name="ro_food_allowance" />
                            <field name="ro_transportation" />
                            <field name="ro_equalizations" />
                            <field name="ro_rewards" />
                        </group>
                        <group name="Deduction">
                            <field name="ro_absence" />
                            <field name="ro_penalties" />
                            <field name="ro_splited_loans" />
                            <field name="ro_loans" />
                            <!-- <field name="ro_discount_returns" /> -->
                        </group>
                   </group>
                   
                </xpath>
            </field>
        </record>
        
    </data>
    
</odoo>