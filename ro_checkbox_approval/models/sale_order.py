from odoo import models, fields, api, SUPERUSER_ID, exceptions
from datetime import datetime

class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    ro_quality_stored = fields.Boolean(compute='_compute_fields')
    ro_logistic_stored = fields.Boolean(compute='_compute_fields')
    ro_finance_stored = fields.Boolean(compute='_compute_fields')
    ro_backhouse_stored = fields.Boolean(compute='_compute_fields')

    ro_quality = fields.Boolean(string="Quality",tracking=True)
    ro_logistic = fields.Boolean(string="Logistic",tracking=True)
    ro_finance = fields.Boolean(string="Finance",tracking=True)
    ro_backhouse = fields.Boolean(string="Backhouse",tracking=True)


     # Compute methods
    @api.depends('ro_quality_stored','ro_logistic_stored','ro_finance_stored','ro_backhouse_stored')
    def _compute_fields(self):
        for record in self:
            record.ro_quality_stored = self.env.user.has_group('ro_checkbox_approval.group_quality')
            record.ro_logistic_stored = self.env.user.has_group('ro_checkbox_approval.group_logistic')
            record.ro_finance_stored = self.env.user.has_group('ro_checkbox_approval.group_finance')
            record.ro_backhouse_stored = self.env.user.has_group('ro_checkbox_approval.group_backhouse')
            
            
            
    ro_date_quality_changed = fields.Date(string="Quality Date Changed",store=True)
    ro_date_logistic_changed = fields.Date(string="Logistic Date Changed",store=True)
    ro_date_finance_changed = fields.Date(string="Finance Date Changed",store=True)
    ro_date_backhouse_changed = fields.Date(string="Backhouse Date Changed",store=True)
    
    @api.model
    def write(self, vals):
        current_date = datetime.today().date()

        # Date change logic
        for record in self:
            if 'ro_quality' in vals:
                record.ro_date_quality_changed = current_date if vals['ro_quality'] else False
            if 'ro_logistic' in vals:
                record.ro_date_logistic_changed = current_date if vals['ro_logistic'] else False
            if 'ro_finance' in vals:
                record.ro_date_finance_changed = current_date if vals['ro_finance'] else False
            if 'ro_backhouse' in vals:
                record.ro_date_backhouse_changed = current_date if vals['ro_backhouse'] else False

        # Activity completion logic
        approval_checkboxes = ['ro_quality', 'ro_logistic', 'ro_finance', 'ro_backhouse']
        for checkbox in approval_checkboxes:
            if vals.get(checkbox) == True:
                group = self.checkbox_to_group(checkbox)
                self.activity_ids.with_user(SUPERUSER_ID).search([('user_id', 'in', group.users.ids)]).action_done()

        return super(SaleOrder, self).write(vals)



            
    def checkbox_to_group(self, checkbox_name):
        mapping = {
            'ro_quality': 'group_quality',
            'ro_logistic': 'group_logistic',
            'ro_finance': 'group_finance',
            'ro_backhouse': 'group_backhouse',
        }
        return self.env.ref('ro_checkbox_approval.' + mapping[checkbox_name])

    # @api.model
    # def create(self, vals):
    #     record = super(SaleOrder, self).create(vals)

    #     all_groups = ['group_quality', 'group_logistic', 'group_finance', 'group_backhouse']

    #     for group_xml_id in all_groups:
    #         group = self.env.ref('ro_checkbox_approval.' + group_xml_id)
    #         for user in group.users:
    #             activity_vals = {
    #                 'res_id': record.id,
    #                 'res_model_id': self.env['ir.model']._get('sale.order').id,
    #                 'user_id': user.id,
    #                 'summary': f'Approval required for order {record.name}',
    #                 'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
    #             }
    #             self.env['mail.activity'].with_user(SUPERUSER_ID).create(activity_vals)

    #     return record
    @api.model
    def create(self, vals):
        record = super(SaleOrder, self).create(vals)

        if self.env.context.get('copy', False):
            return record  

        all_groups = ['group_quality', 'group_logistic', 'group_finance', 'group_backhouse']
        activities = []
        all_users = self.env['res.users']
        for group_xml_id in all_groups:
            group = self.env.ref('ro_checkbox_approval.' + group_xml_id)
            all_users |= group.users
    
        for user in all_users:
            activities.append({
                'res_id': record.id,
                'res_model_id': self.env['ir.model']._get('sale.order').id,
                'user_id': user.id,
                'summary': f'Approval required for order {record.name}',
                'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
            })

        if len(activities)>0:
            self.env['mail.activity'].with_user(SUPERUSER_ID).create(activities)

        return record


    
    
    def action_confirm(self):
        if not (self.ro_quality and self.ro_logistic and 
                self.ro_finance and self.ro_backhouse):
            raise exceptions.UserError("All checkboxes must be checked before confirming the sale order.")

        return super(SaleOrder, self).action_confirm()

    
  
    def action_draft(self):
        # Resetting the checkboxes
        self.write({
            'ro_quality': False,
            'ro_logistic': False,
            'ro_finance': False,
            'ro_backhouse': False,
            'ro_date_quality_changed': False,
            'ro_date_logistic_changed': False,
            'ro_date_finance_changed': False,
            'ro_date_backhouse_changed': False,
        })

        # Recreate scheduled activities
        all_groups = ['group_quality', 'group_logistic', 'group_finance', 'group_backhouse']
        for group_xml_id in all_groups:
            group = self.env.ref('ro_checkbox_approval.' + group_xml_id)
            for user in group.users:
                activity_vals = {
                    'res_id': self.id,
                    'res_model_id': self.env['ir.model']._get('sale.order').id,
                    'user_id': user.id,
                    'summary': f'Approval required for order {self.name}',
                    'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                }
                self.env['mail.activity'].with_user(SUPERUSER_ID).create(activity_vals)

        return super(SaleOrder, self).action_draft()

    



  

