<odoo>
  <data>

    <record id="view_order_form_inherit" model="ir.ui.view">
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale.view_order_form"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='partner_id']" position="after">
          <field name="ro_quality_stored" invisible="1"/>
          <field name="ro_logistic_stored" invisible="1"/>
          <field name="ro_finance_stored" invisible="1"/>
          <field name="ro_backhouse_stored" invisible="1"/>

          <field name="ro_quality" readonly= 'not ro_quality_stored'/>
          <field name="ro_logistic" readonly='not ro_logistic_stored'/>
          <field name="ro_finance" readonly='not ro_finance_stored'/>
          <field name="ro_backhouse" readonly='not ro_backhouse_stored'/>
        </xpath>
      </field>
    </record>


    <record id="view_order_tree_inherit" model="ir.ui.view">
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale.sale_order_tree"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='partner_id']" position="after">
          <field name="ro_date_quality_changed" optional="hide"/>
          <field name="ro_date_logistic_changed" optional="hide"/>
          <field name="ro_date_finance_changed" optional="hide"/>
          <field name="ro_date_backhouse_changed" optional="hide"/>

        </xpath>
      </field>
    </record>



  </data>
</odoo>

