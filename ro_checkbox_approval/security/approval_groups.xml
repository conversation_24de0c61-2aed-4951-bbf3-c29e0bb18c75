<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
        <record id="group_quality" model="res.groups">
            <field name="name">quality [Xeed]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>



        <record id="group_logistic" model="res.groups">
            <field name="name">logistic [Xeed]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>



        <record id="group_finance" model="res.groups">
            <field name="name">finance [Xeed]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>



        <record id="group_backhouse" model="res.groups">
            <field name="name">backhouse [Xeed]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>



    </data>

</odoo>


