from odoo import models, fields, api


class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    ro_number_of_boxes = fields.Integer(string="عدد الصناديق")
    ro_box_unit = fields.Many2one(
        'box.unit', string='وحدة جمع الصناديق', help="الوحدة المستخدمة لجمع عدد الصناديق")
    ro_pallet_count = fields.Integer(string='عدد البالتات')
    ro_pallet_unit = fields.Many2one(
        'pallet.unit', string='وحدة جمع البالتات', help="الوحدة المستخدمة لجمع عدد البالتات")

    ro_total_boxes = fields.Float(
        string="إجمالي الصناديق", compute="_compute_totals", store=True)
    ro_total_pallets = fields.Float(
        string="إجمالي البالتات", compute="_compute_totals", store=True)
    ro_gross_qty = fields.Float(string="gross qty", store=True)

    @api.depends('ro_number_of_boxes', 'ro_box_unit', 'ro_pallet_count', 'ro_pallet_unit')
    def _compute_totals(self):
        for line in self:
            line.ro_total_boxes = line.ro_number_of_boxes * \
                (line.ro_box_unit.unit_value if line.ro_box_unit else 0)
            line.ro_total_pallets = line.ro_pallet_count * \
                (line.ro_pallet_unit.unit_value if line.ro_pallet_unit else 0)

    @api.depends('ro_gross_qty', 'ro_total_boxes', 'ro_total_pallets')
    def _compute_product_qty(self):
        for line in self:
            if line.ro_total_boxes == 0 and line.ro_total_pallets == 0:
                line.product_qty = line.ro_gross_qty
            else:
                line.product_qty = line.ro_gross_qty - (line.ro_total_boxes + line.ro_total_pallets)

