<odoo>
  <record id="action_box_unit_config" model="ir.actions.act_window">
    <field name="name">combine unit</field>
    <field name="res_model">box.unit</field>
    <field name="view_mode">tree,form</field>
  </record>
  <record id="action_pallet_unit_config" model="ir.actions.act_window">
    <field name="name">pallet unit</field>
    <field name="res_model">pallet.unit</field>
    <field name="view_mode">tree,form</field>
  </record>

  <record id="model_pallet_unit_form" model="ir.ui.view">
    <field name="model">pallet.unit</field>
    <field name="arch" type="xml">
      <form>
        <sheet>
          <group>
            <field name="unit_value"/>
          </group>
        </sheet>
      </form>
    </field>
  </record>

  <record id="model_box_unit_form" model="ir.ui.view">
    <field name="model">box.unit</field>
    <field name="arch" type="xml">
      <form>
        <sheet>
          <group>
            <field name="unit_value"/>
          </group>
        </sheet>
      </form>
    </field>
  </record>

  <record id="model_box_unit_tree" model="ir.ui.view">
    <field name="model">box.unit</field>
    <field name="arch" type="xml">
      <tree>
        <field name="unit_value"/>
      </tree>
    </field>
  </record>

  <record id="model_pallet_unit_tree" model="ir.ui.view">
    <field name="model">pallet.unit</field>
    <field name="arch" type="xml">
      <tree string="pallet">
        <field name="unit_value"/>
      </tree>
    </field>
  </record>
  <menuitem id="menu_pallet_unit_config" name="وحدة جمع البالتات" sequence="2" action="action_pallet_unit_config" parent="purchase.menu_purchase_config"/>
  <menuitem id="menu_unit_config" name="وحدة جمع الصندوق" sequence="3" action="action_box_unit_config" parent="purchase.menu_purchase_config"/>

</odoo>
