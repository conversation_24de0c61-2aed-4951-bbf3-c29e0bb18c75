<?xml version="1.0" encoding="utf-8"?>
<odoo>
 <record id="paperformat_packing_list" model="report.paperformat">
            <field name="name">Packing List</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">50</field>
            <field name="margin_bottom">30</field>
            <!-- <field name="margin_bottom">74</field> -->
            <field name="margin_left">10</field>
            <field name="margin_right">10</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">40</field>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">110</field>
        </record>

  
    <template id="packing_list_external_layout">
        <div class="header" t-att-style="report_header_style">
            <div class="row">
                <div>
                <center>
                     <h3 class="fw-bold">Packing List</h3>
                 </center> 
                     <img t-att-src="'/ro_packing_list_print/static/src/img/logo.png'" alt="Company Logo" style="float: left; margin-right:10px;"/>
                </div>
               
            </div>
        </div>

         <div t-attf-class="article o_report_layout_standard " t-attf-style="background-image: url('/ro_packing_list_print/static/src/img/water_mark.png');" t-att-data-oe-model="o[0] and o[0]._name" t-att-data-oe-id="o[0] and o[0].id" t-att-data-oe-lang="o[0] and o[0].env.context.get('lang')">   
         
            <div>
                <t t-call="web.address_layout"/>
            </div>
            <t t-out="0"/>
          
           
        </div>


    </template>

    <record id="action_packing_list" model="ir.actions.report">
            <field name="name">Packing List</field>
            <field name="model">stock.picking</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_packing_list_print.packing_list_report_template</field>
            <field name="report_file">ro_packing_list_print.packing_list_report_template</field>
            <field name="print_report_name">'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)</field>
            <field name="paperformat_id" ref="ro_packing_list_print.paperformat_packing_list"/>
            <field name="binding_model_id" ref="stock.model_stock_picking"/>
            <field name="binding_type">report</field>
        </record>


    <template id="packing_list_report_template">
            <t t-call="web.html_container">
                <!-- <t t-set="origin_lists" t-value="docs.mapped(lambda x:x.origin)"/>
                <t t-foreach="origin_lists" t-as="origin_list"> -->
                <!-- <t t-foreach="docs" t-as="o"> -->
                    <!-- <t t-set="o" t-value="docs.filtered(lambda x:x.origin == origin_list)"/> -->
                    <t t-set="o" t-value="docs"/>
                    <t t-call="ro_packing_list_print.packing_list_external_layout">
                        <link rel="preconnect" href="https://fonts.googleapis.com"/>
                        <link rel="preconnect" href="https://fonts.gstatic.com"/>
                        <link href="https://fonts.googleapis.com/css2?family=PT+Serif:ital,wght@0,400;0,700;1,400;1,700" rel="stylesheet"/>
                        <div class="page">
                          <div class="row no-outline" style="width: 100%; display: flex; justify-content: space-between; border:none;">
                                   <style>
                                   .no-border tbody{border:0px !important;}
                                   </style>
                                   
                                    <div class="col-9" style="text-align: left;">
                                    <strong><div style="font-size:12px;padding-bottom:3px">XEED FOR IMPORT AND EXPORT</div></strong>
                                    <div style="font-size:12px;padding-bottom:3px">Building No. 37, Walk of Cairo Trade Center,</div>
                                    <div style="font-size:12px;">Kilo 38, Cairo-Alexandria Desert Road - Sheikh Zayed</div>

                                    </div>
                                    
                                    
                            </div>
                            <span t-esc="origin_lists"/>
                            
                         <b><hr style="border: 1px solid #000; width:100%;font-weight: bold;"/></b>
                    

                        <div class="oe_structure"/>
                        <div class="row">
                        <table class="borderless;" style="width:100%;font-family:'PT Serif',serif;">
                            <tr>
                                <td style="direction: ltr;width:150px;">
                                       <span>
                                        <b style="text-align:left;">Order No.</b>
                                       </span>
                                </td>
                                <td> 
                                    <span>
                                        <b t-field="o[0].origin" style="direction:ltr;text-align:left;"/> 
                                    </span>
                                   
                                </td>
                                <td style="padding-left:30px;border: none; direction: ltr;width:280px">
                                    <span>
                                        <b style="text-align:left;">Arrival Port /Destination</b> 
                                   
                                    </span>
                                </td>
                                <td>
                                    <span>
                                        <b t-field="o[0].sale_id.destination_port" style="direction:ltr;text-align:left;">

                                        </b> 
                                    </span>
                                </td>


                            </tr>
                            <tr>
                                <td style="border: none; direction: ltr;">
                                    <span>
                                        <b style="text-align:left;">Client</b> 
                                    </span>
                                         
                                </td>
                                <td>
                                    <span>
                                        <b t-field="o[0].partner_id" style="direction:ltr;text-align:left;">
                                        </b>
                                    </span> 

                                </td>
                                <td style="padding-left:30px;border: none; direction: ltr;">  
                                    <span>
                                        <b style="text-align:left;">Loading Port </b> 

                                    </span>
                                </td>
                                <td>
                                    <span>
                                        <b t-field="o[0].sale_id.loading_port" style="direction:ltr;text-align:left;">
                                        </b>  
                                    </span> 
                                </td>
                                       
                                    
                            </tr>
                            <tr>
                                <td style="border: none; direction: ltr;">
                                    <span>
                                        <b style="text-align:left;">Product</b> 

                                    </span>
                                </td>
                                <td>
                                    <span>

                                    <t t-set="products" t-value="o.move_ids_without_package.mapped(lambda x:x.product_id)"/>
                                    <t t-foreach="products" t-as="product">
                                            <t t-set="variant" t-value="product.product_template_variant_value_ids.filtered(lambda x:x.attribute_id.ro_variety == True)"/>
                                            <b t-esc="product.name"/>
                                            <t t-if="variant">
                                                <b>(</b>
                                                <b t-esc="', '.join(variant.product_attribute_value_id.mapped('name'))"/>
                                                <b>)</b>
                                            </t>
                                            
                                            <br/>
                                    </t>
                                    </span>
                                    
                                </td>
                                <td style="padding-left:30px;border: none; direction: ltr;">
                                <span>
                                    <b style="text-align:left;">Vessel Name</b> 

                                </span>
                                </td>
                                <td>
                                    <span>
                                        <b t-field="o[0].sale_id.vessel_name" style="direction:ltr;text-align:left;"/> </span>

                                </td>
                                        
                             
                               

                            </tr> 
                             <tr>
                                <td style="border: none; direction: ltr;">
                                    <span>
                                        <b style="text-align:left;">Shipping Method</b> 

                                    </span>
                                </td>
                                <td>
                                    <span><b t-field="o[0].sale_id.shipping_method_id" style="direction:ltr;text-align:left;"/> </span>

                                </td>
                                <td style="padding-left:30px;border: none; direction: ltr;width:280px">
                                    <span>
                                        <b style="text-align:left;">Shipping Line</b> 

                                    </span>
                                </td>
                                <td>
                                    <span>
                                        <b t-field="o[0].carrier_id" style="direction:ltr;text-align:left;">
                                            
                                        </b> </span>
                                      
                                </td>

                                    
                            </tr>
                            <tr>
                                <td style="border: none; direction: ltr;">
                                        <span>
                                            <b style="text-align:left;">BL.No.</b> 
                                        </span>
                                </td>
                                <td>

                                    <span>
                                        <b t-field="o[0].sale_id.bl_number" style="direction:ltr;text-align:left;"/> </span>

                                </td>
                                <td style="padding-left:30px;border: none; direction: ltr;width:280px">
                                    <span>
                                        <b style="text-align:left;">Loading Date</b> 

                                    </span>
                                </td>
                                <td>
                                    <span>
                                        <b t-esc="o[0].sale_id.loading_date.strftime('%d-%b-%Y') if o[0].loading_date else '' " style="direction:ltr;text-align:left;">

                                        </b> 
                                    </span>

                                </td>
                                        
                            

                                       
                            </tr> 
                            <tr>
                                <td style="border: none; direction: ltr;">
                                    <span>
                                        <b style="text-align:left;">ETD</b> 

                                    </span>
                                </td>
                                <td>
                                    <span><b t-esc="o[0].sale_id.etd_date.strftime('%d-%b-%Y') if o[0].sale_id.eta_date else ''" style="direction:ltr;text-align:left;"/> </span>

                                </td>
                                <td style="padding-left:30px;border: none; direction: ltr;width:280px">
                                    <span>
                                        <b style="text-align:left;">ETA</b> 

                                    </span>
                                </td>
                                <td>
                                    <span><b t-esc="o[0].sale_id.eta_date.strftime('%d-%b-%Y') if o[0].sale_id.eta_date else '' " style="direction:ltr;text-align:left;"/> </span>

                                </td>
                                    

                                       
                            </tr>
                                
                        </table>
                    
                        </div>

                        <table class="table-sm o_main_table table-bordered" style="width:100%;margin-top:14px">
                            <thead style="background-color:#03413A;color:white;font-size:10px">
                                <tr style="border:1px solid white;">
                                    <th name="th_sn" class="text-center" rowspan="2"><span>SN</span></th>
                                    <th name="th_container_no" class="text-center" rowspan="2"><span>Container No</span></th>
                                    <th name="th_variety" class="text-center" rowspan="2"><span>Variety</span></th>
                                    <th name="th_class" class="text-center" rowspan="2" style="width:80px"><span>Class</span></th>
                                    <!-- <th name="th_carton" class="text-center"><span>Carton</span></th> -->
                                    <th name="th_net_weight_kg" class="text-center" rowspan="2"><span>Net Weight KG</span></th>
                                    <th name="th_gross_weight_kg" class="text-center" rowspan="2"><span>Gross Weight KG</span></th>
                                    <th name="th_size" class="text-center" t-att-colspan="len(o.ro_cargo_line_ids.mapped('ro_product_size'))"><span>SIZE</span>
                                    </th>
                                    
                                    <th name="th_total_carton" class="text-center" rowspan="2"><span>Total Carton</span></th>
                                    <th name="th_total_pallet" class="text-center" rowspan="2"><span>Total Pallet</span></th>
                                    <th name="th_total_net_weight" class="text-center" rowspan="2"><span>Total Net Weight KG</span></th>
                                    <th name="th_total_gross_weight" class="text-center" rowspan="2"><span>Total Gross Weight KG</span></th>
                                </tr>
                                <tr style="border:1px solid white;">
                                    <t t-foreach="o.ro_cargo_line_ids.mapped('ro_product_size')" t-as="size">
                                    <th>
                                        <span t-esc="size.name"/>
                                    </th>

                                    </t>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set="counter" t-value="1"/>
                                <t t-set="net_weight_total_counter" t-value="0"/>
                                <t t-set="gross_weight_total_counter" t-value="0"/>
                                <t t-set="product_uom_qty_counter" t-value="0"/>
                                <t t-set="total_pallet_count" t-value="0"/>
                                <t t-set="total_size_counter" t-value="0"/>
                                <t t-foreach="o" t-as="picking_id">
                                    <t t-set="operation_lines_product" t-value="picking_id.move_ids_without_package.mapped('product_id')"/>

                                    <t t-foreach="operation_lines_product" t-as="op_line">

                                        <t t-value="picking_id.move_ids_without_package.filtered(lambda x:x.product_id == op_line)[0]" t-set="line"/>

                                        <t t-set="net_weight_total_counter" t-value="net_weight_total_counter + ((line.product_id.weight or 0.0 )* (line.product_uom_qty or 0.0))"/>
                                        <t t-set="gross_weight_total_counter" t-value="gross_weight_total_counter + ((line.product_id.ro_gross_weight or 0.0 )* (line.product_uom_qty or 0.0))"/>
                                        <t t-set="product_uom_qty_counter" t-value="product_uom_qty_counter + line.product_uom_qty"/>
                                        <!-- <t t-set="total_pallet_count" t-value="total_pallet_count + len(o.move_line_ids.filtered(lambda x:x.product_id.id == line.product_id.id).mapped('package_id'))"/> -->
                                        <t t-set="total_pallet_count" t-value="total_pallet_count + len(picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/>
                                        <!-- <b><span t-esc="len(o.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/></b> -->
                                        

                                    
                                    
                                        <tr>
                                            <!-- SN -->
                                            <td>
                                                <t t-esc="counter"/>
                                                <t t-set="counter" t-value="counter + 1"/>
                                            
                                            </td>
                                            <!-- container_no -->
                                            <td name="td_container_no"><span t-field="picking_id.carrier_tracking_ref"/></td>
                                        
                                        
                                        
                                                <t t-if="line.product_id.product_template_attribute_value_ids.attribute_id">
                                                    <td class="td" style="border: 1px solid #000; padding: 8px;text-align:center;">
                                                        <t t-foreach="line.product_id.product_template_attribute_value_ids" t-as="attribute_line">
                                                            <t t-if="attribute_line.attribute_id.ro_variety">
                                                                <t t-esc="attribute_line.name"/>
                                                            </t>
                                                        </t>

                                                    </td>
                                                </t>
                                                <t t-else="">
                                                    <td class="td" style="border: 1px solid #000; padding: 8px;text-align:center;"/>
                                                </t>
                                                <!-- class -->
                                            <td name="td_class">
                                                    <!-- need to add class attribute from product -->
                                                    <!-- <span t-field="picking_id.ro_class"/> -->
                                                    <t t-set="class" t-value="line.product_id.product_template_variant_value_ids.filtered(lambda x:x.attribute_id.ro_class == True)"/>
                                                    <t t-if="class">
                                                        <span t-esc="', '.join(class.product_attribute_value_id.mapped('name'))"/>
                                                    </t>
                                                        
                                            </td>

                                            <td name="td_net_weight"><span t-esc="int(line.product_id.weight)"/></td>
                                            <td name="td_gross_weight"><span t-esc="(line.product_id.ro_gross_weight)"/></td>
                                        
                                        
                                            <t t-set="package_cargo" t-value="picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id)"/>

                                            <!-- get sizes  -->     
                                            <t t-set="size_cargo" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                            <t t-set="total_sizes" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                            <!-- <t t-esc="total_sizes"></t> -->
                                                <td t-if="not size_cargo"/>
                                                <t t-else="">
                                                    <t t-foreach="size_cargo" t-as="packege_size">
                                                    <t t-if="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))!= 0">
                                                        <td>
                                                            <t t-set="total_size_counter" t-value="total_size_counter + len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"/>

                                                            <span t-esc="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"/>
                                                        </td>
                                                    </t>
                                                    <t t-else="">
                                                        <td/>
                                                    </t>
                                                    
                                                </t>
                                            </t>
                                
                                        
                                        
                                        
                                        
                                                <!-- </t> -->
                                    
                                                                        
                                            
                                            
                                            <td>
                                                <t t-esc="line.product_uom_qty"/>

                                            </td>
                                            <td>
                                                <!-- <span t-esc="len(o.move_line_ids.filtered(lambda x:x.product_id.id == line.product_id.id).mapped('package_id'))"/> -->
                                                <span t-esc="len(picking_id.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/>
                                            
                                            </td>
                            
                                            <td>
                                                <t t-set="net_weight" t-value="line.product_id.weight or 0.0"/>
                                                <!-- Demand -->
                                                <t t-set="quantity" t-value="line.product_uom_qty or 0.0"/>
                                                <t t-if="net_weight and quantity">
                                                    <span t-esc="int(net_weight * quantity)"/>
                                                </t>
                                            </td>

                                
                                            <td>
                                                <t t-set="gross_weight" t-value="line.product_id.ro_gross_weight or 0.0"/>
                                                <t t-if="gross_weight and quantity">
                                                    <span t-esc="int(gross_weight * quantity)"/>
                                                </t>
                                            </td>
                                    


                                        </tr>
                                
                                    
                                    </t>
                                </t>
                                <tr style="height:35px">
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>

                                    <td>
                                        <t t-set="package_cargo" t-value="o.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id)"/>
    <!--                                             
                                            </t>
                                        </t> -->

                                    <!-- get sizes  -->     
                                        <t t-set="size_cargo" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                        <t t-set="total_sizes" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                        <!-- <t t-esc="total_sizes"></t> -->
                                            <td t-if="not size_cargo"/>
                                            <t t-else="">
                                                <t t-foreach="size_cargo" t-as="packege_size">
                                                <t t-if="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))!= 0">
                                                    <td>
                                                        <t t-set="total_size_counter" t-value="total_size_counter + len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"/>

                                                        <!-- <span t-esc="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"></span> -->
                                                    </td>
                                                </t>
                                                <t t-else="">
                                                    <td/>
                                                </t>
                                                
                                            </t>
                                        </t>
                                    </td>
                                        
                                        
                                        
                                        
                                                <!-- </t> -->
                                    
                                                                        
                                            
                                            
                                    <td>
                                    </td>
                                    
                                
                                
                                    <td/>
                                    <td/>
                                    
                                </tr>
                                <tr style="height:35px">
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>
                                    <td/>

                                    <td>
                                        <t t-set="package_cargo" t-value="o.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id)"/>
    <!--                                             
                                                </t>
                                        </t> -->

                                        <!-- get sizes  -->     
                                        <t t-set="size_cargo" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                        <t t-set="total_sizes" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                        <!-- <t t-esc="total_sizes"></t> -->
                                            <td t-if="not size_cargo"/>
                                            <t t-else="">
                                                <t t-foreach="size_cargo" t-as="packege_size">
                                                <t t-if="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))!= 0">
                                                    <td>
                                                        <t t-set="total_size_counter" t-value="total_size_counter + len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"/>

                                                        <!-- <span t-esc="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"></span> -->
                                                    </td>
                                                </t>
                                                <t t-else="">
                                                    <td/>
                                                </t>
                                                
                                            </t>
                                        </t>
                                    </td>
                                        
                                        
                                        
                                        
                                                <!-- </t> -->
                                    
                                                                        
                                            
                                            
                                    <td>
                                    </td>
                                    
                                
                                
                                    <td/>
                                    <td/>
                                    
                                </tr>


                            <tr style="height:35px">
                                <td/>
                                <td/>
                                <td/>
                                <td/>
                                <td/>
                                <td/>

                                <td>
                                    <t t-set="package_cargo" t-value="o.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id)"/>
<!--                                             
                                            </t>
                                    </t> -->

                                    <!-- get sizes  -->     
                                    <t t-set="size_cargo" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                    <t t-set="total_sizes" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                    <!-- <t t-esc="total_sizes"></t> -->
                                        <td t-if="not size_cargo"/>
                                        <t t-else="">
                                            <t t-foreach="size_cargo" t-as="packege_size">
                                            <t t-if="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))!= 0">
                                                <td>
                                                    <t t-set="total_size_counter" t-value="total_size_counter + len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"/>

                                                    <!-- <span t-esc="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"></span> -->
                                                </td>
                                            </t>
                                            <t t-else="">
                                                <td/>
                                            </t>
                                            
                                        </t>
                                    </t>
                                </td>
                                    
                                    
                                    
                                    
                                            <!-- </t> -->
                                
                                                                       
                                        
                                        
                            <td>
                                </td>
                                
                              
                             
                                <td/>
                                <td/>
                                
                            </tr>
                            <tr style="height:35px">
                                <td/>
                                <td/>
                                <td/>
                                <td/>
                                <td/>
                                <td/>

                                <td>
                                    <t t-set="package_cargo" t-value="o.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id)"/>
<!--                                             
                                        </t>
                                </t> -->

                               <!-- get sizes  -->     
                                <t t-set="size_cargo" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                <t t-set="total_sizes" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                <!-- <t t-esc="total_sizes"></t> -->
                                    <td t-if="not size_cargo"/>
                                    <t t-else="">
                                        <t t-foreach="size_cargo" t-as="packege_size">
                                        <t t-if="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))!= 0">
                                            <td>
                                                <t t-set="total_size_counter" t-value="total_size_counter + len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"/>

                                                <!-- <span t-esc="len(package_cargo.filtered(lambda y:y.ro_product_size == packege_size))"></span> -->
                                            </td>
                                        </t>
                                        <t t-else="">
                                            <td/>
                                        </t>
                                        
                                    </t>
                                </t>
                               </td>
                                    
                                    
                                    
                                    
                                            <!-- </t> -->
                                
                                                                       
                                        
                                        
                                <td>
                                </td>
                                
                              
                             
                                <td/>
                                <td/>
                                
                            </tr>
                            
                            <tr>
                                <th name="th_total_offer" colspan="6" class="text-center">Total</th>  
                                    
                                <t t-set="size_cargo" t-value="o.ro_cargo_line_ids.mapped('ro_product_size')"/>
                                
                                <t t-if="not size_cargo">
                                    <td/>
                                </t>
                                <t t-else="">
                                        <t t-foreach="size_cargo" t-as="packege_size">
                                        
                                        <td>
                                            <span t-esc="len(o.ro_cargo_line_ids.filtered(lambda y:y.ro_product_size == packege_size))"/>

                                        </td>
                                        
                                    </t>
                                </t>

                                <td>
                                    <span><t t-esc="product_uom_qty_counter"/></span>
                                </td> 
                                <td>
                                    <!-- <t t-if="o.o.ro_cargo_line_ids.ro_package"> -->
                                    <!-- <span t-esc="len(o.ro_cargo_line_ids.filtered(lambda x:x.ro_product.id == line.product_id.id).mapped('ro_package'))"/> -->

                                        <span><t t-esc="total_pallet_count"/></span>
                                    <!-- </t> -->
                                </td> 
                                <td>
                                    <t t-esc="int(net_weight_total_counter)"/>
                                    
                                </td> 
                                <td>
                                    <t t-esc="int(gross_weight_total_counter)"/>

                                    
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    

                <table>
                    <t t-set="counter" t-value="1"/>
                        <!-- <t t-foreach="o.move_ids_without_package" t-as="line"> -->
                    <t t-foreach="o" t-as="picking_id">
                        
                        <t t-foreach="picking_id.ro_cargo_line_ids" t-as="cargo_line">
                            <tr>
                                <t t-if="cargo_line.ro_data_logger or cargo_line.ro_euro_pallate">
                                    <!-- <t t-if="cargo_line.ro_data_logger or cargo_line.ro_pallet_code or cargo_line.ro_euro"> -->
                                    <td style="padding:3px;width:500px" class="fw-bold">
                                        <t t-esc="counter"/>
                                        <t t-set="counter" t-value="counter + 1"/> -
                            
                                        <!-- Create a list to hold the items -->
                                        <t t-set="items" t-value="[]"/>
                                        
                                        <span style="padding-left:1px">Data Logger in</span>
                                        <t t-if="picking_id.carrier_tracking_ref">
                                            (<t t-esc="picking_id.carrier_tracking_ref" style="padding-left:1px"/>) :

                                        </t>
                                      
                                        <!-- <t t-if="cargo_line.ro_pallet_code "> -->
                                            <!-- <t t-esc="cargo_line.ro_data_logger"/> -->
                                        
                                        <!-- </t> -->
                                        
                                          <!-- logger value -->
                                          <t t-if="cargo_line.ro_data_logger and not cargo_line.ro_euro_pallate">
                                            <t t-set="items" t-value="items + ['pallet : ' + cargo_line.ro_data_logger + ', Size: '+ ','.join (cargo_line.ro_product_size.mapped('name'))]"/>
                                            <!-- <t t-set="items" t-value="items + ['pallet  ('+ cargo_line.ro_package.name + ') : ' + cargo_line.ro_data_logger]" /> -->
                                            

                       

                                        </t>
                                        <t t-elif="cargo_line.ro_euro_pallate and cargo_line.ro_data_logger">

                                            <t t-set="items" t-value="items + ['Euro Pallet : ' + cargo_line.ro_data_logger + ', Size: '+ ','.join (cargo_line.ro_product_size.mapped('name'))]"/>
                                            <!-- <t t-set="items" t-value="items + ['pallet  ('+ cargo_line.ro_package.name + ') : ' + cargo_line.ro_data_logger]" /> -->
                                            

                       

                                        </t>
                                        
                                        <t t-elif="cargo_line.ro_euro_pallate and not cargo_line.ro_data_logger">
                                            <t t-set="items" t-value="items + ['Euro pallet'+ ', Size: '+ ','.join (cargo_line.ro_product_size.mapped('name'))]"/>

                                            <!--<t t-set="items" t-value="items + ['Euro Pallet : ' + cargo_line.ro_data_logger + ', Size: '+ .join (cargo_line.ro_product_size.mapped('name'))]"/>-->
                                            <!-- <t t-set="items" t-value="items + ['pallet  ('+ cargo_line.ro_package.name + ') : ' + cargo_line.ro_data_logger]" /> -->
                                            

                       

                                        </t>
                                        <t t-else="">
                                            <t t-set="items" t-value="items + ['pallet']"/>
                                            

                       

                                        </t>
                                        
                                       
                                        <!-- <t t-if="cargo_line.ro_euro">
                                            <t t-set="items" t-value="items + ['Euro Pallet size : ' + cargo_line.ro_euro]" />
                                        </t> -->
                            
                                        <t t-set="joined_items" t-value="', '.join(items)"/>
                                        <t t-esc="joined_items"/>
                                    </td>
                                </t>
                            </tr>
                            
                      
                    
                        </t>
                    </t>
                </table>


                    <div style="margin-top:25px">
                        <b> <p style="text-align:center;">Egyptian Tax Registration Number: ***********</p></b>
                        <div class="row">
                                        <div class="col-4" style="text-align:center;">
                                            <img t-att-src="'ro_packing_list_print//static/src/img/email.png'" alt="email" style="height:25px;wedth:25px;"/>
                                            <a t-attf-href="{'mailto:%s'|format('<EMAIL>')}" style="color: blue; text-decoration: underline;">
                                                <EMAIL>
                                            </a>
                                        
                                        </div>
                                    
                                        <!-- <div style="float:center; text-align: center;"> -->
                                        <div class="col-4" style="text-align:center;">
                                            <img t-att-src="'ro_packing_list_print//static/src/img/world-wide-web.png'" alt="web" style="height:25px;wedth:25px;"/>
                                            <a t-att-href="'http://www.xeedcorp.com/'" style="color: blue; text-decoration: underline;">
                                                                www.xeedcorp.com
                                            </a>
    
                                        </div>
                                    
                                        <!-- <div style="float:right; text-align: right; margin-bottom:100px;"> -->
                                        <div class="col-4" style="text-align:center;">
                                            <img t-att-src="'ro_packing_list_print//static/src/img/telephone.png'" alt="phone" style="height:25px;wedth:25px;"/>
                                            <strong><span style="color: blue;">+2010 915 56 556</span></strong>
                                        </div>
                        </div>
                    </div>
                
                    <p style="page-break-before:always;"/>                       
                    </div>

                        
            
                </t>
            <!-- </t> -->
        </t>
            
</template>

    </odoo>