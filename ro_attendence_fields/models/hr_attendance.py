# -*- coding: utf-8 -*-

from collections import defaultdict
from datetime import datetime, time
from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import UserError


class PurchaseRequisition(models.Model):
    _inherit = "hr.attendance"

    delay_ignore = fields.Boolean()
    ignore_note = fields.Char()

    less_hours = fields.Float(string='Less Hours', store=True, compute='get_amount_less')
    note = fields.Char(compute="get_attendance_note")

    delay_created = fields.Many2one('hr.employee.delays')

    @api.depends('worked_hours','employee_id')
    def get_amount_less(self):
        for rec in self:
            work_hours = rec.employee_id.resource_calendar_id.hours_per_day
            rec.less_hours = work_hours - rec.worked_hours if rec.worked_hours and rec.worked_hours < work_hours else 0

    def get_attendance_note(self):
        for rec in self:
            rec.note = ""
            if rec.less_hours > 0:
                rec.note = "Less Time"
            if rec.overtime_hours > 0:
                rec.note = "Over Time"
            

    def create_employee_delay(self):
        for employee in self.mapped('employee_id'):
            
            att_lines = self.filtered(lambda x: x.employee_id == employee and not x.delay_created and not x.delay_ignore)

            less_hours = sum(att_lines.mapped('less_hours'))

            if less_hours > 8:
               delay = self.env['hr.employee.delays'].create({
                    'date':att_lines[-1].check_in.date(),
                    'ro_delays_employee': employee.id,
                    'ro_delays': less_hours-8,
                })
               att_lines.delay_created = delay