<odoo>
    <data>

        <record id="view_hr_attendance_tree_inherit" model="ir.ui.view">
            <field name="model">hr.attendance</field>
            <field name="inherit_id" ref="hr_attendance.view_attendance_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='overtime_hours']" position="after">
                    <field name="less_hours" string="Less Time" optional="show" widget="float_time"/>
                    <field name="note" string="سجلات أخرى" optional="show"/>

                    <field name="delay_ignore" optional="show" />
                    <field name="ignore_note" optional="show" />
                    
                </xpath>
            </field>
        </record>

        <record id="view_hr_attendance_form_inherit" model="ir.ui.view">
            <field name="model">hr.attendance</field>
            <field name="inherit_id" ref="hr_attendance.hr_attendance_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='overtime_hours']" position="after">
                    <field name="less_hours" widget="float_time" string="Less Time"/>
                    <field name="delay_ignore" />
                    <field name="ignore_note" required="delay_ignore" />
                    <field name="delay_created" invisible="1"/>

                </xpath>
            </field>
        </record>


        <record model="ir.actions.server" id="hr_employee_delays_action">
            <field name="name">Create Delay</field>
            <field name="model_id" ref="hr_attendance.model_hr_attendance"/>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="binding_model_id" ref="hr_attendance.model_hr_attendance"/>
            <field name="binding_view_types">list</field>    
            <field name="code">records.create_employee_delay()</field>
        </record>

    </data>
</odoo>