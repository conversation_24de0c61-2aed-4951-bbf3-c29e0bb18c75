# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.osv import expression
from odoo.exceptions import ValidationError

class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    ro_employee_id = fields.Char(string='Employee ID', copy=False)

    _sql_constraints = [
        ('unique_employee_id', 'unique(ro_employee_id)', 'Employee ID must be unique!'),
    ]

    @api.constrains('ro_employee_id')
    def _check_unique_employee_id(self):
        for record in self:
            if record.ro_employee_id:
                existing_employee = self.search([('ro_employee_id', '=', record.ro_employee_id), ('id', '!=', record.id)])
                if existing_employee:
                    raise ValidationError('Employee ID must be unique!')
    
    # @api.model
    # def _name_search(self, name, domain=None, operator='ilike', limit=None, order=None):
    #     domain = domain or []
    #     if operator != 'ilike' or not (name or '').strip():
    #         # ignore 'ilike' with name containing only spaces
    #         domain = expression.AND([['|', ('name', operator, name), ('ro_employee_id', operator, name)], domain])
    #     return self._search(domain, limit=limit, order=order)
