<odoo>
  <data>
           <record id="hr_employee_form_inherited_id" model="ir.ui.view" >
            <field name="name">hr.employee.form.inherited</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='avatar_128']" position="before">
                   <label for="ro_employee_id" string="Employee ID"/>
                   <field name="ro_employee_id" string="Employee ID" />
                             
                              
                </xpath>
              
            </field>
        </record>



         <record id="hr_employee_search_inherited_id" model="ir.ui.view" >
            <field name="name">hr.employee.search.inherited</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_filter"/>
            <field name="arch" type="xml">
             <xpath expr="//field[@name='name']" position="attributes">
                    <!-- <field name="ro_department_code"/> -->
                  <attribute name="filter_domain">['|', ('name', 'ilike', self),('ro_employee_id', 'ilike', self)]</attribute>
                </xpath>
              
            </field>
        </record>
  </data>
</odoo>

<!-- filter_domain="['|', ('name', 'ilike', self),('ro_employee_id', 'ilike', self)]" -->